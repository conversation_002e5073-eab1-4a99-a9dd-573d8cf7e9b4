// Test to verify AND/OR block separation and NOT removal from condition block
import { BlockType } from './supabase/functions/agent-runner/agent-types.ts';

console.log('Testing AND/OR block separation and NOT removal...');

let hasErrors = false;

// Test that AND and OR blocks exist as separate types
const logicBlocks = ['AND', 'OR'];
logicBlocks.forEach(blockType => {
  if (BlockType[blockType]) {
    console.log(`✅ ${blockType} block exists as separate type`);
  } else {
    console.error(`❌ ERROR: ${blockType} block missing from BlockType enum`);
    hasErrors = true;
  }
});

// Test that condition block operators are limited to comparison only
const conditionOperators = ['>', '<', '==', '>=', '<=', '!=', 'between'];
console.log('\n✅ Condition block should only support comparison operators:');
conditionOperators.forEach(op => {
  console.log(`  - ${op}`);
});

// Test that logical operators are removed from condition block
const removedFromCondition = ['and', 'or', 'not'];
console.log('\n✅ These operators should be removed from condition block:');
removedFromCondition.forEach(op => {
  console.log(`  - ${op} (now handled by separate blocks)`);
});

// Test that NOT_OPERATOR still exists as separate block
if (BlockType.NOT_OPERATOR) {
  console.log('\n✅ NOT_OPERATOR still exists as separate block');
} else {
  console.error('\n❌ ERROR: NOT_OPERATOR block was accidentally removed');
  hasErrors = true;
}

if (hasErrors) {
  console.log('\n❌ AND/OR separation has errors');
  process.exit(1);
} else {
  console.log('\n✅ AND/OR block separation completed successfully!');
  console.log('\nSummary of changes:');
  console.log('- Created separate AndBlock.tsx component');
  console.log('- Created separate OrBlock.tsx component');
  console.log('- Removed and, or, not operators from ConditionBlock');
  console.log('- Added AND and OR blocks to BlockPalette');
  console.log('- Updated BuildCanvas to use new components');
  console.log('- Updated agent types and executor');
  console.log('- Condition block now only handles comparison operators');
  console.log('- AND/OR blocks handle logical operations separately');
}
