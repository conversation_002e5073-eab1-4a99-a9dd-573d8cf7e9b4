// Simple test to verify block cleanup worked correctly
import { BlockType } from './supabase/functions/agent-runner/agent-types.ts';

console.log('Testing block cleanup...');

// Test that deleted blocks are no longer in the enum
const deletedBlocks = [
  'SIGNAL_CONFIRMATION',
  'IF_THEN_ELSE', 
  'AND_OPERATOR',
  'OR_OPERATOR',
  'SIGNAL'
];

let hasErrors = false;

deletedBlocks.forEach(blockType => {
  if (BlockType[blockType]) {
    console.error(`❌ ERROR: ${blockType} still exists in BlockType enum`);
    hasErrors = true;
  } else {
    console.log(`✅ ${blockType} successfully removed`);
  }
});

// Test that remaining blocks still exist
const remainingBlocks = [
  'CONDITION',
  'TRIGGER',
  'NOT_OPERATOR',
  'TIME_FILTER',
  'MARKET_CONDITION_FILTER'
];

remainingBlocks.forEach(blockType => {
  if (BlockType[blockType]) {
    console.log(`✅ ${blockType} still exists (correct)`);
  } else {
    console.error(`❌ ERROR: ${blockType} was accidentally removed`);
    hasErrors = true;
  }
});

if (hasErrors) {
  console.log('\n❌ Block cleanup has errors');
  process.exit(1);
} else {
  console.log('\n✅ Block cleanup completed successfully!');
  console.log('\nSummary of changes:');
  console.log('- Removed SIGNAL_CONFIRMATION block');
  console.log('- Removed IF_THEN_ELSE block (replaced with basic CONDITION block)');
  console.log('- Removed AND_OPERATOR and OR_OPERATOR blocks (duplicates of AND/OR)');
  console.log('- Removed SIGNAL block (duplicate of TRIGGER)');
  console.log('- Kept CONDITION block and renamed it to "Condition"');
  console.log('- Cleaned up all references in related files');
}
