-- Supabase SQL Setup for Trade Sensei Watchlist Functionality
-- Run this in your Supabase SQL Editor to enable watchlist features

-- 1. Create the user_watchlist table (matching existing service)
CREATE TABLE IF NOT EXISTS public.user_watchlist (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    symbol VARCHAR(10) NOT NULL,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    current_price DECIMAL(10,4) DEFAULT 0,
    price_change DECIMAL(10,4) DEFAULT 0,
    price_change_percent DECIMAL(5,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure unique symbol per user
    UNIQUE(user_id, symbol)
);

-- 2. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_watchlist_user_id ON public.user_watchlist(user_id);
CREATE INDEX IF NOT EXISTS idx_user_watchlist_symbol ON public.user_watchlist(symbol);
CREATE INDEX IF NOT EXISTS idx_user_watchlist_created_at ON public.user_watchlist(created_at DESC);

-- 3. Enable Row Level Security (RLS)
ALTER TABLE public.user_watchlist ENABLE ROW LEVEL SECURITY;

-- 4. Create RLS policies
-- Users can only see their own watchlist items
CREATE POLICY "Users can view own watchlist" ON public.user_watchlist
    FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own watchlist items
CREATE POLICY "Users can insert own watchlist" ON public.user_watchlist
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own watchlist items
CREATE POLICY "Users can update own watchlist" ON public.user_watchlist
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own watchlist items
CREATE POLICY "Users can delete own watchlist" ON public.user_watchlist
    FOR DELETE USING (auth.uid() = user_id);

-- 5. Create a function to get user's watchlist with real-time price data
CREATE OR REPLACE FUNCTION get_user_watchlist(user_uuid UUID)
RETURNS TABLE (
    id UUID,
    symbol VARCHAR(10),
    name VARCHAR(255),
    current_price DECIMAL(10,4),
    price_change DECIMAL(10,4),
    price_change_percent DECIMAL(5,2),
    created_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        w.id,
        w.symbol,
        w.name,
        w.current_price,
        w.price_change,
        w.price_change_percent,
        w.created_at
    FROM public.user_watchlist w
    WHERE w.user_id = user_uuid
    ORDER BY w.created_at DESC;
END;
$$;

-- 6. Create a function to add stock to watchlist (with duplicate handling)
CREATE OR REPLACE FUNCTION add_to_watchlist(
    user_uuid UUID,
    stock_symbol VARCHAR(10),
    stock_name VARCHAR(255)
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSON;
    watchlist_id UUID;
BEGIN
    -- Try to insert, handle duplicate gracefully
    INSERT INTO public.user_watchlist (user_id, symbol, name, created_at, updated_at)
    VALUES (user_uuid, UPPER(stock_symbol), stock_name, NOW(), NOW())
    ON CONFLICT (user_id, symbol)
    DO UPDATE SET
        name = EXCLUDED.name,
        updated_at = NOW()
    RETURNING id INTO watchlist_id;
    
    -- Return success response
    SELECT json_build_object(
        'success', true,
        'message', 'Stock added to watchlist successfully',
        'id', watchlist_id,
        'symbol', UPPER(stock_symbol),
        'name', stock_name
    ) INTO result;
    
    RETURN result;
EXCEPTION
    WHEN OTHERS THEN
        -- Return error response
        SELECT json_build_object(
            'success', false,
            'message', 'Failed to add stock to watchlist: ' || SQLERRM
        ) INTO result;
        
        RETURN result;
END;
$$;

-- 7. Create a function to remove stock from watchlist
CREATE OR REPLACE FUNCTION remove_from_watchlist(
    user_uuid UUID,
    stock_symbol VARCHAR(10)
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSON;
    rows_affected INTEGER;
BEGIN
    -- Delete the watchlist item
    DELETE FROM public.user_watchlist
    WHERE user_id = user_uuid AND symbol = UPPER(stock_symbol);
    
    GET DIAGNOSTICS rows_affected = ROW_COUNT;
    
    IF rows_affected > 0 THEN
        -- Return success response
        SELECT json_build_object(
            'success', true,
            'message', 'Stock removed from watchlist successfully',
            'symbol', UPPER(stock_symbol)
        ) INTO result;
    ELSE
        -- Return not found response
        SELECT json_build_object(
            'success', false,
            'message', 'Stock not found in watchlist'
        ) INTO result;
    END IF;
    
    RETURN result;
EXCEPTION
    WHEN OTHERS THEN
        -- Return error response
        SELECT json_build_object(
            'success', false,
            'message', 'Failed to remove stock from watchlist: ' || SQLERRM
        ) INTO result;
        
        RETURN result;
END;
$$;

-- 8. Create a function to check if stock is in user's watchlist
CREATE OR REPLACE FUNCTION is_in_watchlist(
    user_uuid UUID,
    stock_symbol VARCHAR(10)
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    found BOOLEAN := FALSE;
BEGIN
    SELECT EXISTS(
        SELECT 1 FROM public.user_watchlist
        WHERE user_id = user_uuid AND symbol = UPPER(stock_symbol)
    ) INTO found;
    
    RETURN found;
END;
$$;

-- 9. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON public.watchlist TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_watchlist(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION add_to_watchlist(UUID, VARCHAR, VARCHAR) TO authenticated;
GRANT EXECUTE ON FUNCTION remove_from_watchlist(UUID, VARCHAR) TO authenticated;
GRANT EXECUTE ON FUNCTION is_in_watchlist(UUID, VARCHAR) TO authenticated;

-- 10. Insert some sample data (optional - remove in production)
-- This is just for testing purposes
/*
INSERT INTO public.watchlist (user_id, symbol, name) VALUES
    ('00000000-0000-0000-0000-000000000000', 'AAPL', 'Apple Inc.'),
    ('00000000-0000-0000-0000-000000000000', 'TSLA', 'Tesla Inc.'),
    ('00000000-0000-0000-0000-000000000000', 'GOOGL', 'Alphabet Inc.')
ON CONFLICT (user_id, symbol) DO NOTHING;
*/

-- Success message
SELECT 'Watchlist setup completed successfully! 🎉' as status;
