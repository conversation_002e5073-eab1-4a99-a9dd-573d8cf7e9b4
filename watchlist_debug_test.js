// Debug Test for Watchlist Functionality
// Add this to your browser console to test watchlist operations

// Test 1: Check if user is authenticated
console.log('🔍 Testing Watchlist Functionality...');

// Test authentication
async function testAuth() {
  try {
    const { data: { user }, error } = await window.supabase.auth.getUser();
    if (error) {
      console.error('❌ Auth Error:', error);
      return false;
    }
    if (!user) {
      console.error('❌ No user found - please log in');
      return false;
    }
    console.log('✅ User authenticated:', user.id);
    return user;
  } catch (error) {
    console.error('❌ Auth test failed:', error);
    return false;
  }
}

// Test 2: Check if table exists and is accessible
async function testTableAccess() {
  try {
    const { data, error } = await window.supabase
      .from('user_watchlist')
      .select('*')
      .limit(1);
    
    if (error) {
      console.error('❌ Table access error:', error);
      return false;
    }
    console.log('✅ Table accessible, current data:', data);
    return true;
  } catch (error) {
    console.error('❌ Table access test failed:', error);
    return false;
  }
}

// Test 3: Try to add a test stock
async function testAddStock() {
  try {
    const user = await testAuth();
    if (!user) return false;

    const { data, error } = await window.supabase
      .from('user_watchlist')
      .insert({
        user_id: user.id,
        symbol: 'TEST',
        name: 'Test Stock',
        current_price: 100.00,
        price_change: 5.00,
        price_change_percent: 5.00
      });

    if (error) {
      console.error('❌ Insert error:', error);
      return false;
    }
    console.log('✅ Test stock added successfully');
    return true;
  } catch (error) {
    console.error('❌ Add stock test failed:', error);
    return false;
  }
}

// Test 4: Try to fetch watchlist
async function testFetchWatchlist() {
  try {
    const user = await testAuth();
    if (!user) return false;

    const { data, error } = await window.supabase
      .from('user_watchlist')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('❌ Fetch error:', error);
      return false;
    }
    console.log('✅ Watchlist fetched:', data);
    return data;
  } catch (error) {
    console.error('❌ Fetch watchlist test failed:', error);
    return false;
  }
}

// Test 5: Clean up test data
async function cleanupTestData() {
  try {
    const user = await testAuth();
    if (!user) return false;

    const { error } = await window.supabase
      .from('user_watchlist')
      .delete()
      .eq('user_id', user.id)
      .eq('symbol', 'TEST');

    if (error) {
      console.error('❌ Cleanup error:', error);
      return false;
    }
    console.log('✅ Test data cleaned up');
    return true;
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting watchlist debug tests...\n');
  
  console.log('1️⃣ Testing authentication...');
  const authResult = await testAuth();
  if (!authResult) return;
  
  console.log('\n2️⃣ Testing table access...');
  const tableResult = await testTableAccess();
  if (!tableResult) return;
  
  console.log('\n3️⃣ Testing add stock...');
  const addResult = await testAddStock();
  
  console.log('\n4️⃣ Testing fetch watchlist...');
  const fetchResult = await testFetchWatchlist();
  
  console.log('\n5️⃣ Cleaning up test data...');
  await cleanupTestData();
  
  console.log('\n🎉 All tests completed!');
  
  if (authResult && tableResult && addResult && fetchResult) {
    console.log('✅ Watchlist functionality is working correctly!');
    console.log('💡 If stars are not working, check:');
    console.log('   - WatchlistProvider is wrapping your app');
    console.log('   - Console for any React errors');
    console.log('   - Network tab for failed requests');
  } else {
    console.log('❌ Some tests failed - check the errors above');
  }
}

// Instructions
console.log('📋 Watchlist Debug Instructions:');
console.log('1. Make sure you are logged in');
console.log('2. Run: runAllTests()');
console.log('3. Check the results above');
console.log('\nTo run the test, type: runAllTests()');

// Make functions available globally
window.watchlistDebug = {
  testAuth,
  testTableAccess,
  testAddStock,
  testFetchWatchlist,
  cleanupTestData,
  runAllTests
};
