import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { watchlistService, WatchlistItem, WatchlistStock } from '@/services/watchlistService';
import { watchlistPriceService } from '@/services/watchlistPriceService';

interface WatchlistContextType {
  watchlist: WatchlistItem[];
  isLoading: boolean;
  isInWatchlist: (symbol: string) => boolean;
  addToWatchlist: (stock: WatchlistStock) => Promise<boolean>;
  removeFromWatchlist: (symbol: string) => Promise<boolean>;
  refreshWatchlist: () => Promise<void>;
  refreshPrices: () => Promise<void>;
}

const WatchlistContext = createContext<WatchlistContextType | undefined>(undefined);

interface WatchlistProviderProps {
  children: ReactNode;
}

export const WatchlistProvider: React.FC<WatchlistProviderProps> = ({ children }) => {
  const [watchlist, setWatchlist] = useState<WatchlistItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize watchlist on mount
  useEffect(() => {
    const initializeAndStartUpdates = async () => {
      await initializeWatchlist();

      // Wait a moment for watchlist to load, then start price updates
      setTimeout(() => {
        console.log('🎯 WatchlistContext: Starting price updates...');
        watchlistPriceService.startPriceUpdates(120000); // Update every 2 minutes
      }, 2000);
    };

    initializeAndStartUpdates();

    // Cleanup on unmount
    return () => {
      watchlistPriceService.stopPriceUpdates();
    };
  }, []);

  const initializeWatchlist = async () => {
    setIsLoading(true);
    try {
      await watchlistService.initializeCache();
      const items = watchlistService.getCachedWatchlist();
      setWatchlist(items);
    } catch (error) {
      console.error('Error initializing watchlist:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshWatchlist = async () => {
    try {
      console.log('🎯 WatchlistContext: Refreshing watchlist...');
      const items = await watchlistService.getWatchlist();
      console.log('🎯 WatchlistContext: Got items from service:', items);
      setWatchlist(items);
      console.log('🎯 WatchlistContext: Watchlist state updated');
    } catch (error) {
      console.error('🎯 WatchlistContext: Error refreshing watchlist:', error);
    }
  };

  const addToWatchlist = async (stock: WatchlistStock): Promise<boolean> => {
    try {
      console.log('🎯 WatchlistContext: Adding to watchlist', stock);
      const success = await watchlistService.addToWatchlist(stock);
      console.log('🎯 WatchlistContext: Service result:', success);
      if (success) {
        // Update price for the newly added stock
        await watchlistPriceService.updateSingleSymbol(stock.symbol);
        await refreshWatchlist();
        console.log('🎯 WatchlistContext: Watchlist refreshed, new count:', watchlist.length);
      }
      return success;
    } catch (error) {
      console.error('🎯 WatchlistContext: Error adding to watchlist:', error);
      return false;
    }
  };

  const removeFromWatchlist = async (symbol: string): Promise<boolean> => {
    try {
      const success = await watchlistService.removeFromWatchlist(symbol);
      if (success) {
        await refreshWatchlist();
      }
      return success;
    } catch (error) {
      console.error('Error removing from watchlist:', error);
      return false;
    }
  };

  const refreshPrices = async () => {
    try {
      console.log('🎯 WatchlistContext: Manually refreshing prices...');
      await watchlistPriceService.updateAllWatchlistPrices();
      await refreshWatchlist();
      console.log('🎯 WatchlistContext: Price refresh completed');
    } catch (error) {
      console.error('🎯 WatchlistContext: Error refreshing prices:', error);
    }
  };

  const isInWatchlist = (symbol: string): boolean => {
    return watchlist.some(item => item.symbol.toUpperCase() === symbol.toUpperCase());
  };

  const value: WatchlistContextType = {
    watchlist,
    isLoading,
    isInWatchlist,
    addToWatchlist,
    removeFromWatchlist,
    refreshWatchlist,
    refreshPrices
  };

  return (
    <WatchlistContext.Provider value={value}>
      {children}
    </WatchlistContext.Provider>
  );
};

export const useWatchlist = (): WatchlistContextType => {
  const context = useContext(WatchlistContext);
  if (context === undefined) {
    throw new Error('useWatchlist must be used within a WatchlistProvider');
  }
  return context;
};
