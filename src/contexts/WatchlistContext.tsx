import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { watchlistService, WatchlistItem, WatchlistStock } from '@/services/watchlistService';

interface WatchlistContextType {
  watchlist: WatchlistItem[];
  isLoading: boolean;
  isInWatchlist: (symbol: string) => boolean;
  addToWatchlist: (stock: WatchlistStock) => Promise<boolean>;
  removeFromWatchlist: (symbol: string) => Promise<boolean>;
  refreshWatchlist: () => Promise<void>;
}

const WatchlistContext = createContext<WatchlistContextType | undefined>(undefined);

interface WatchlistProviderProps {
  children: ReactNode;
}

export const WatchlistProvider: React.FC<WatchlistProviderProps> = ({ children }) => {
  const [watchlist, setWatchlist] = useState<WatchlistItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize watchlist on mount
  useEffect(() => {
    initializeWatchlist();
  }, []);

  const initializeWatchlist = async () => {
    setIsLoading(true);
    try {
      await watchlistService.initializeCache();
      const items = watchlistService.getCachedWatchlist();
      setWatchlist(items);
    } catch (error) {
      console.error('Error initializing watchlist:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshWatchlist = async () => {
    try {
      const items = await watchlistService.getWatchlist();
      setWatchlist(items);
    } catch (error) {
      console.error('Error refreshing watchlist:', error);
    }
  };

  const addToWatchlist = async (stock: WatchlistStock): Promise<boolean> => {
    try {
      console.log('🎯 WatchlistContext: Adding to watchlist', stock);
      const success = await watchlistService.addToWatchlist(stock);
      console.log('🎯 WatchlistContext: Service result:', success);
      if (success) {
        await refreshWatchlist();
        console.log('🎯 WatchlistContext: Watchlist refreshed, new count:', watchlist.length);
      }
      return success;
    } catch (error) {
      console.error('🎯 WatchlistContext: Error adding to watchlist:', error);
      return false;
    }
  };

  const removeFromWatchlist = async (symbol: string): Promise<boolean> => {
    try {
      const success = await watchlistService.removeFromWatchlist(symbol);
      if (success) {
        await refreshWatchlist();
      }
      return success;
    } catch (error) {
      console.error('Error removing from watchlist:', error);
      return false;
    }
  };

  const isInWatchlist = (symbol: string): boolean => {
    return watchlist.some(item => item.symbol.toUpperCase() === symbol.toUpperCase());
  };

  const value: WatchlistContextType = {
    watchlist,
    isLoading,
    isInWatchlist,
    addToWatchlist,
    removeFromWatchlist,
    refreshWatchlist
  };

  return (
    <WatchlistContext.Provider value={value}>
      {children}
    </WatchlistContext.Provider>
  );
};

export const useWatchlist = (): WatchlistContextType => {
  const context = useContext(WatchlistContext);
  if (context === undefined) {
    throw new Error('useWatchlist must be used within a WatchlistProvider');
  }
  return context;
};
