/**
 * Drawing persistence utility for trading charts
 * Handles saving and loading drawings per stock symbol using localStorage
 */

import { Drawing } from './drawingCoordinates';

const STORAGE_KEY_PREFIX = 'trading_chart_drawings_';
const STORAGE_VERSION = '1.0';

export interface PersistedDrawing extends Drawing {
  version?: string;
  timestamp?: number;
}

export interface DrawingStorage {
  version: string;
  timestamp: number;
  drawings: PersistedDrawing[];
}

/**
 * Get the storage key for a specific symbol
 */
function getStorageKey(symbol: string): string {
  return `${STORAGE_KEY_PREFIX}${symbol.toUpperCase()}`;
}

/**
 * Save drawings for a specific symbol to localStorage
 */
export function saveDrawingsForSymbol(symbol: string, drawings: Drawing[]): void {
  try {
    const storageData: DrawingStorage = {
      version: STORAGE_VERSION,
      timestamp: Date.now(),
      drawings: drawings.map(drawing => ({
        ...drawing,
        version: STORAGE_VERSION,
        timestamp: Date.now()
      }))
    };

    const key = getStorageKey(symbol);
    localStorage.setItem(key, JSON.stringify(storageData));
    
    console.log(`💾 Saved ${drawings.length} drawings for ${symbol}`);
  } catch (error) {
    console.error('Error saving drawings to localStorage:', error);
  }
}

/**
 * Load drawings for a specific symbol from localStorage
 */
export function loadDrawingsForSymbol(symbol: string): Drawing[] {
  try {
    const key = getStorageKey(symbol);
    const stored = localStorage.getItem(key);
    
    if (!stored) {
      console.log(`📂 No saved drawings found for ${symbol}`);
      return [];
    }

    const storageData: DrawingStorage = JSON.parse(stored);
    
    // Version check - if version doesn't match, return empty array
    if (storageData.version !== STORAGE_VERSION) {
      console.log(`🔄 Drawing storage version mismatch for ${symbol}, clearing old data`);
      localStorage.removeItem(key);
      return [];
    }

    console.log(`📂 Loaded ${storageData.drawings.length} drawings for ${symbol}`);
    return storageData.drawings;
  } catch (error) {
    console.error('Error loading drawings from localStorage:', error);
    return [];
  }
}

/**
 * Clear all drawings for a specific symbol
 */
export function clearDrawingsForSymbol(symbol: string): void {
  try {
    const key = getStorageKey(symbol);
    localStorage.removeItem(key);
    console.log(`🗑️ Cleared all drawings for ${symbol}`);
  } catch (error) {
    console.error('Error clearing drawings from localStorage:', error);
  }
}

/**
 * Get all symbols that have saved drawings
 */
export function getSymbolsWithDrawings(): string[] {
  try {
    const symbols: string[] = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(STORAGE_KEY_PREFIX)) {
        const symbol = key.replace(STORAGE_KEY_PREFIX, '');
        symbols.push(symbol);
      }
    }
    
    return symbols.sort();
  } catch (error) {
    console.error('Error getting symbols with drawings:', error);
    return [];
  }
}

/**
 * Get drawing count for a specific symbol
 */
export function getDrawingCountForSymbol(symbol: string): number {
  try {
    const drawings = loadDrawingsForSymbol(symbol);
    return drawings.length;
  } catch (error) {
    console.error('Error getting drawing count:', error);
    return 0;
  }
}

/**
 * Clear all drawings for all symbols (use with caution)
 */
export function clearAllDrawings(): void {
  try {
    const symbols = getSymbolsWithDrawings();
    symbols.forEach(symbol => clearDrawingsForSymbol(symbol));
    console.log(`🗑️ Cleared drawings for ${symbols.length} symbols`);
  } catch (error) {
    console.error('Error clearing all drawings:', error);
  }
}

/**
 * Export drawings for a symbol (for backup/sharing)
 */
export function exportDrawingsForSymbol(symbol: string): string | null {
  try {
    const key = getStorageKey(symbol);
    const stored = localStorage.getItem(key);
    return stored;
  } catch (error) {
    console.error('Error exporting drawings:', error);
    return null;
  }
}

/**
 * Import drawings for a symbol (from backup/sharing)
 */
export function importDrawingsForSymbol(symbol: string, data: string): boolean {
  try {
    // Validate the data first
    const storageData: DrawingStorage = JSON.parse(data);
    
    if (!storageData.drawings || !Array.isArray(storageData.drawings)) {
      throw new Error('Invalid drawing data format');
    }

    const key = getStorageKey(symbol);
    localStorage.setItem(key, data);
    
    console.log(`📥 Imported ${storageData.drawings.length} drawings for ${symbol}`);
    return true;
  } catch (error) {
    console.error('Error importing drawings:', error);
    return false;
  }
}

/**
 * Auto-save drawings with debouncing to prevent excessive localStorage writes
 */
let autoSaveTimeout: NodeJS.Timeout | null = null;

export function autoSaveDrawings(symbol: string, drawings: Drawing[], debounceMs: number = 1000): void {
  if (autoSaveTimeout) {
    clearTimeout(autoSaveTimeout);
  }
  
  autoSaveTimeout = setTimeout(() => {
    saveDrawingsForSymbol(symbol, drawings);
    autoSaveTimeout = null;
  }, debounceMs);
}

/**
 * Get storage usage statistics
 */
export function getStorageStats(): {
  totalSymbols: number;
  totalDrawings: number;
  storageSize: number;
} {
  try {
    const symbols = getSymbolsWithDrawings();
    let totalDrawings = 0;
    let storageSize = 0;
    
    symbols.forEach(symbol => {
      const drawings = loadDrawingsForSymbol(symbol);
      totalDrawings += drawings.length;
      
      const key = getStorageKey(symbol);
      const stored = localStorage.getItem(key);
      if (stored) {
        storageSize += stored.length;
      }
    });
    
    return {
      totalSymbols: symbols.length,
      totalDrawings,
      storageSize
    };
  } catch (error) {
    console.error('Error getting storage stats:', error);
    return {
      totalSymbols: 0,
      totalDrawings: 0,
      storageSize: 0
    };
  }
}
