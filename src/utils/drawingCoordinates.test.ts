/**
 * Test file for drawing coordinate conversion utilities
 */

import {
  ChartBounds,
  AbsolutePoint,
  RelativePoint,
  Drawing,
  convertToRelativeCoordinates,
  convertToAbsoluteCoordinates,
  convertDrawingToRelative,
  convertDrawingToAbsolute,
  calculateChartBounds,
  updateDrawingsForNewTimeframe,
  storeDrawingWithBothCoordinates
} from './drawingCoordinates';

// Mock chart data for testing
const mockStockData = [
  { time: '2024-01-01T10:00:00Z', open: 100, high: 105, low: 95, close: 102 },
  { time: '2024-01-01T11:00:00Z', open: 102, high: 108, low: 98, close: 106 },
  { time: '2024-01-01T12:00:00Z', open: 106, high: 110, low: 104, close: 108 },
  { time: '2024-01-01T13:00:00Z', open: 108, high: 112, low: 106, close: 110 },
  { time: '2024-01-01T14:00:00Z', open: 110, high: 115, low: 108, close: 113 }
];

// Test chart bounds calculation
console.log('Testing calculateChartBounds...');
const bounds = calculateChartBounds(mockStockData);
console.log('Chart bounds:', bounds);

// Test coordinate conversion
console.log('\nTesting coordinate conversion...');
const testPoint: AbsolutePoint = {
  time: '2024-01-01T12:00:00Z',
  price: 107
};

const relativePoint = convertToRelativeCoordinates(testPoint, bounds);
console.log('Absolute point:', testPoint);
console.log('Relative point:', relativePoint);

const backToAbsolute = convertToAbsoluteCoordinates(relativePoint, bounds);
console.log('Back to absolute:', backToAbsolute);

// Test drawing conversion
console.log('\nTesting drawing conversion...');
const testDrawing: Drawing = {
  id: 1,
  type: 'line',
  points: [
    { time: '2024-01-01T10:30:00Z', price: 101 },
    { time: '2024-01-01T13:30:00Z', price: 109 }
  ]
};

const relativeDrawing = convertDrawingToRelative(testDrawing, bounds);
console.log('Original drawing:', testDrawing);
console.log('Relative drawing:', relativeDrawing);

// Simulate new timeframe with different bounds
const newBounds: ChartBounds = {
  timeMin: new Date('2024-01-01T09:00:00Z').getTime(),
  timeMax: new Date('2024-01-01T15:00:00Z').getTime(),
  priceMin: 90,
  priceMax: 120
};

const adjustedDrawing = convertDrawingToAbsolute(relativeDrawing, newBounds);
console.log('Adjusted drawing for new timeframe:', adjustedDrawing);

// Test multiple drawings update
console.log('\nTesting multiple drawings update...');
const drawings: Drawing[] = [
  {
    id: 1,
    type: 'line',
    points: [
      { time: '2024-01-01T10:30:00Z', price: 101 },
      { time: '2024-01-01T13:30:00Z', price: 109 }
    ],
    relativePoints: [
      { timePercent: 0.25, pricePercent: 0.3 },
      { timePercent: 0.75, pricePercent: 0.7 }
    ]
  },
  {
    id: 2,
    type: 'circle',
    center: { time: '2024-01-01T12:00:00Z', price: 107 },
    radiusPoint: { time: '2024-01-01T13:00:00Z', price: 110 },
    relativeCenter: { timePercent: 0.5, pricePercent: 0.6 },
    relativeRadiusPoint: { timePercent: 0.75, pricePercent: 0.75 }
  }
];

const updatedDrawings = updateDrawingsForNewTimeframe(drawings, newBounds);
console.log('Updated drawings:', updatedDrawings);

console.log('\nAll tests completed successfully!');
