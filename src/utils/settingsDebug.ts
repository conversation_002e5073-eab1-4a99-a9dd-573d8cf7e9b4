/**
 * Debug utilities for settings page to help identify freezing issues
 */

export const debugSettings = {
  /**
   * Monitor save operations for performance issues
   */
  monitorSaveOperation: (operationName: string) => {
    const startTime = performance.now();
    console.log(`🔍 [Settings Debug] Starting ${operationName}...`);
    
    return {
      end: (success: boolean = true) => {
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        console.log(`🔍 [Settings Debug] ${operationName} ${success ? 'completed' : 'failed'} in ${duration.toFixed(2)}ms`);
        
        if (duration > 5000) {
          console.warn(`⚠️ [Settings Debug] ${operationName} took longer than 5 seconds (${duration.toFixed(2)}ms)`);
        }
        
        return duration;
      }
    };
  },

  /**
   * Log component mount/unmount cycles
   */
  logComponentLifecycle: (componentName: string, action: 'mount' | 'unmount') => {
    console.log(`🔄 [Settings Debug] ${componentName} ${action}ed at ${new Date().toISOString()}`);
  },

  /**
   * Monitor API calls for hanging requests
   */
  monitorApiCall: async <T>(
    apiCall: () => Promise<T>, 
    callName: string, 
    timeoutMs: number = 10000
  ): Promise<T> => {
    const monitor = debugSettings.monitorSaveOperation(`API Call: ${callName}`);
    
    try {
      const timeoutPromise = new Promise<never>((_, reject) => 
        setTimeout(() => reject(new Error(`${callName} timed out after ${timeoutMs}ms`)), timeoutMs)
      );

      const result = await Promise.race([apiCall(), timeoutPromise]);
      monitor.end(true);
      return result;
    } catch (error) {
      monitor.end(false);
      console.error(`❌ [Settings Debug] ${callName} failed:`, error);
      throw error;
    }
  },

  /**
   * Check for memory leaks in settings components
   */
  checkMemoryUsage: () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      console.log(`💾 [Settings Debug] Memory usage:`, {
        used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
        total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
        limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`
      });
    }
  },

  /**
   * Log state changes that might cause re-renders
   */
  logStateChange: (stateName: string, oldValue: any, newValue: any) => {
    if (oldValue !== newValue) {
      console.log(`🔄 [Settings Debug] State change: ${stateName}`, {
        from: oldValue,
        to: newValue,
        timestamp: new Date().toISOString()
      });
    }
  }
};

// Export for use in components during development
export default debugSettings;
