import { supabase } from '@/integrations/supabase/client';

export interface WatchlistItem {
  id: string;
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  addedAt: string;
  userId: string;
}

export interface WatchlistStock {
  symbol: string;
  name: string;
}

class WatchlistService {
  private static instance: WatchlistService;
  private watchlistCache: WatchlistItem[] = [];
  private watchlistSymbols: Set<string> = new Set();

  static getInstance(): WatchlistService {
    if (!WatchlistService.instance) {
      WatchlistService.instance = new WatchlistService();
    }
    return WatchlistService.instance;
  }

  // Get user's watchlist
  async getWatchlist(): Promise<WatchlistItem[]> {
    try {
      console.log('📊 WatchlistService: Getting watchlist...');
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.log('📊 WatchlistService: No user found');
        return [];
      }

      console.log('📊 WatchlistService: User found:', user.id);

      const { data, error } = await supabase
        .from('user_watchlist')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('📊 WatchlistService: Error fetching watchlist:', error);
        return [];
      }

      console.log('📊 WatchlistService: Raw data from DB:', data);

      // Transform data to match WatchlistItem interface
      const watchlistItems: WatchlistItem[] = (data || []).map(item => ({
        id: item.id,
        symbol: item.symbol,
        name: item.name || `${item.symbol} Inc.`,
        price: item.current_price || 0,
        change: item.price_change || 0,
        changePercent: item.price_change_percent || 0,
        addedAt: item.created_at,
        userId: item.user_id
      }));

      console.log('📊 WatchlistService: Transformed items:', watchlistItems);

      // Update cache
      this.watchlistCache = watchlistItems;
      this.watchlistSymbols = new Set(watchlistItems.map(item => item.symbol));

      return watchlistItems;
    } catch (error) {
      console.error('Error in getWatchlist:', error);
      return [];
    }
  }

  // Add stock to watchlist
  async addToWatchlist(stock: WatchlistStock): Promise<boolean> {
    try {
      console.log('📊 WatchlistService: Adding to watchlist', stock);
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.log('📊 WatchlistService: No user found');
        return false;
      }

      // Check if already in watchlist
      if (this.isInWatchlist(stock.symbol)) {
        console.log('📊 WatchlistService: Already in watchlist', stock.symbol);
        return true; // Already added
      }

      const { error } = await supabase
        .from('user_watchlist')
        .insert({
          user_id: user.id,
          symbol: stock.symbol.toUpperCase(),
          name: stock.name,
          current_price: 0, // Will be updated by price service
          price_change: 0,
          price_change_percent: 0
        });

      if (error) {
        console.error('📊 WatchlistService: Error adding to watchlist:', error);
        return false;
      }

      // Update cache
      this.watchlistSymbols.add(stock.symbol.toUpperCase());
      await this.getWatchlist(); // Refresh cache
      console.log('📊 WatchlistService: Successfully added to watchlist', stock.symbol);

      return true;
    } catch (error) {
      console.error('📊 WatchlistService: Error in addToWatchlist:', error);
      return false;
    }
  }

  // Remove stock from watchlist
  async removeFromWatchlist(symbol: string): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return false;

      const { error } = await supabase
        .from('user_watchlist')
        .delete()
        .eq('user_id', user.id)
        .eq('symbol', symbol.toUpperCase());

      if (error) {
        console.error('Error removing from watchlist:', error);
        return false;
      }

      // Update cache
      this.watchlistSymbols.delete(symbol.toUpperCase());
      this.watchlistCache = this.watchlistCache.filter(item => item.symbol !== symbol.toUpperCase());

      return true;
    } catch (error) {
      console.error('Error in removeFromWatchlist:', error);
      return false;
    }
  }

  // Check if stock is in watchlist
  isInWatchlist(symbol: string): boolean {
    return this.watchlistSymbols.has(symbol.toUpperCase());
  }

  // Get cached watchlist (for quick access)
  getCachedWatchlist(): WatchlistItem[] {
    return this.watchlistCache;
  }

  // Initialize watchlist cache
  async initializeCache(): Promise<void> {
    await this.getWatchlist();
  }

  // Update stock prices (called by price service)
  async updateStockPrice(symbol: string, price: number, change: number, changePercent: number): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { error } = await supabase
        .from('user_watchlist')
        .update({
          current_price: price,
          price_change: change,
          price_change_percent: changePercent,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.id)
        .eq('symbol', symbol.toUpperCase());

      if (error) {
        console.error('Error updating stock price:', error);
        return;
      }

      // Update cache
      const itemIndex = this.watchlistCache.findIndex(item => item.symbol === symbol.toUpperCase());
      if (itemIndex !== -1) {
        this.watchlistCache[itemIndex] = {
          ...this.watchlistCache[itemIndex],
          price,
          change,
          changePercent
        };
      }
    } catch (error) {
      console.error('Error in updateStockPrice:', error);
    }
  }
}

export const watchlistService = WatchlistService.getInstance();
