import { ChartDataPoint, ChartConfig } from '@/components/charts/SocialMediaChartGenerator';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

export interface AIChartRequest {
  query: string;
  userId?: string;
}

export interface AIChartResponse {
  success: boolean;
  chartConfig?: ChartConfig;
  data?: ChartDataPoint[];
  error?: string;
  reasoning?: string;
}

/**
 * Generate chart data using AI based on natural language query
 */
export async function generateChartDataWithAI(request: AIChartRequest): Promise<AIChartResponse> {
  try {
    // Get the current session
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return {
        success: false,
        error: 'Authentication required. Please log in to use AI chart generation.'
      };
    }

    // Call the Supabase edge function
    const { data, error } = await supabase.functions.invoke('ai-chart-generator', {
      body: {
        query: request.query
      },
      headers: {
        Authorization: `Bearer ${session.access_token}`
      }
    });

    if (error) {
      console.error('Error calling ai-chart-generator function:', error);
      return {
        success: false,
        error: error.message || 'Failed to generate chart'
      };
    }

    if (!data || !data.success) {
      console.error('AI chart generation failed:', data);
      return {
        success: false,
        error: data?.error || 'Failed to generate chart'
      };
    }

    console.log('AI chart generation response:', data);
    console.log('Chart config data:', data.chartConfig?.data);

    // Validate that we have data
    if (!data.chartConfig?.data || !Array.isArray(data.chartConfig.data) || data.chartConfig.data.length === 0) {
      console.warn('No data in AI response');
      return {
        success: false,
        error: 'No data received from AI'
      };
    }

    // Transform the data to the correct format
    const transformedConfig = transformChartData(data.chartConfig);

    console.log('Final transformed config:', transformedConfig);
    console.log('Final transformed data:', transformedConfig.data);

    return {
      success: true,
      chartConfig: transformedConfig,
      reasoning: data.reasoning
    };

  } catch (error) {
    console.error('Error generating chart data with AI:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Transform AI-generated chart data to the correct format
 */
function transformChartData(chartConfig: any): ChartConfig {
  console.log('Transforming chart data:', chartConfig);

  if (!chartConfig.data || !Array.isArray(chartConfig.data)) {
    console.warn('Invalid data structure, using fallback');
    return generateFallbackChart('Invalid data').chartConfig!;
  }

  const rawData = chartConfig.data;

  // Check if data is already in the correct format (has 'value' property)
  if (rawData.length > 0 && 'value' in rawData[0]) {
    console.log('Data already in correct format');
    return chartConfig as ChartConfig;
  }

  // Check if this is comparison data (multiple companies/metrics)
  const firstItem = rawData[0];
  const dataKeys = Object.keys(firstItem).filter(key => key !== 'name');

  console.log('Data keys found:', dataKeys);

  if (dataKeys.length === 1) {
    // Single metric data - transform to simple format
    const transformedData: ChartDataPoint[] = rawData.map((item: any) => ({
      name: item.name,
      value: item[dataKeys[0]]
    }));

    return {
      ...chartConfig,
      data: transformedData
    } as ChartConfig;

  } else if (dataKeys.length > 1) {
    // Multiple metrics - create stacked bar chart or use first metric
    console.log('Multiple metrics detected, using first metric:', dataKeys[0]);

    const transformedData: ChartDataPoint[] = rawData.map((item: any) => ({
      name: item.name,
      value: item[dataKeys[0]]
    }));

    return {
      ...chartConfig,
      data: transformedData,
      title: `${chartConfig.title} - ${dataKeys[0]}`,
      subtitle: `Showing ${dataKeys[0]} data (${dataKeys.length} metrics available)`
    } as ChartConfig;
  }

  // Fallback if we can't parse the data
  console.warn('Could not parse data structure, using fallback');
  return generateFallbackChart('Data parsing failed').chartConfig!;
}

/**
 * Generate fallback chart data when AI fails
 */
function generateFallbackChart(query: string): AIChartResponse {
  console.log('Generating fallback chart for query:', query);

  // Generate sample data based on query keywords
  const sampleData: ChartDataPoint[] = [
    { name: 'Q1 2024', value: 1200 },
    { name: 'Q2 2024', value: 1450 },
    { name: 'Q3 2024', value: 1680 },
    { name: 'Q4 2024', value: 1920 },
    { name: 'Q1 2025', value: 2150 }
  ];

  const chartConfig: ChartConfig = {
    title: `Sample Chart: ${query}`,
    subtitle: 'Generated with sample data (AI service unavailable)',
    type: 'bar',
    data: sampleData,
    colors: ['#3B82F6'],
    showValues: true,
    showGrid: true,
    backgroundColor: '#FFFFFF',
    textColor: '#000000',
    width: 800,
    height: 600,
    brandText: 'Made with Osis.co',
    totalChange: '79.2%',
    cagr: '15.8%'
  };

  return {
    success: true,
    chartConfig,
    reasoning: 'Using fallback sample data as AI service is currently unavailable'
  };
}

/**
 * Generate sample queries for user guidance
 */
export const sampleQueries = [
  "Show me Apple's revenue growth over the last 5 quarters",
  "Compare Tesla vs Ford stock performance this year",
  "Apple vs Microsoft revenue comparison",
  "Netflix vs Disney subscriber growth comparison",
  "Create a chart of US GDP growth by quarter",
  "Show the top 5 tech companies by market cap",
  "Display Bitcoin price trend over the last 6 months",
  "Compare Amazon vs Google quarterly revenue",
  "Chart the unemployment rate in the US over time",
  "Show Netflix subscriber growth by year",
  "Compare revenue of major streaming services",
  "iPhone vs Samsung market share comparison",
  "Display inflation rates for the last 10 years",
  "Show renewable energy adoption by country",
  "Compare Coca-Cola vs Pepsi revenue over time"
];
