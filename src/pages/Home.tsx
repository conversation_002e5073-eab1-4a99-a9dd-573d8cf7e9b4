import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Target,
  BarChart3,
  Zap,
  Activity,
  TrendingUp,
  CheckCircle2,
  Circle,

} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useGamification } from '@/contexts/GamificationContext';
import { useWatchlist } from '@/contexts/WatchlistContext';
import ReactECharts from 'echarts-for-react';
import { useQuery } from '@tanstack/react-query';
import { fetchPolygonChartData } from '@/services/polygonService';

interface UserStats {
  agentsCreated: number;
  scansCompleted: number;
  backtestsCompleted: number;
  portfoliosCreated: number;
  winRate: number;
  totalTrades: number;
  successfulTrades: number;
  timeSavedHours: number;
  lastActivityDate: string | null;
}

interface UserAgent {
  id: string;
  name: string;
  description: string;
  returnPercentage: number;
  winRate: number;
  totalTrades: number;
  isActive: boolean;
  bestBacktest?: {
    returnPercentage: number;
    winRate: number;
    totalTrades: number;
    period: string;
  };
}





interface ProgressStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  action: () => void;
  completed: boolean;
}

const Home: React.FC = () => {
  const navigate = useNavigate();
  const { userProgress } = useGamification();
  const { watchlist: watchlistStocks, refreshWatchlist } = useWatchlist();

  // State for real user data
  const [userStats, setUserStats] = useState<UserStats>({
    agentsCreated: 0,
    scansCompleted: 0,
    backtestsCompleted: 0,
    portfoliosCreated: 0,
    winRate: 0,
    totalTrades: 0,
    successfulTrades: 0,
    timeSavedHours: 0,
    lastActivityDate: null
  });
  const [isLoading, setIsLoading] = useState(true);
  const [userName, setUserName] = useState('User');



  const mockAgents: UserAgent[] = [
    {
      id: '1',
      name: 'Momentum Hunter',
      description: 'Identifies strong momentum stocks with technical analysis',
      returnPercentage: 24.5,
      winRate: 72,
      totalTrades: 45,
      isActive: true,
      bestBacktest: { returnPercentage: 24.5, winRate: 72, totalTrades: 45, period: '3M' }
    },
    {
      id: '2',
      name: 'Value Seeker',
      description: 'Finds undervalued opportunities using fundamental analysis',
      returnPercentage: 18.2,
      winRate: 68,
      totalTrades: 32,
      isActive: false,
      bestBacktest: { returnPercentage: 18.2, winRate: 68, totalTrades: 32, period: '6M' }
    },
    {
      id: '3',
      name: 'Breakout Trader',
      description: 'Captures breakout patterns with volume confirmation',
      returnPercentage: 31.8,
      winRate: 65,
      totalTrades: 28,
      isActive: true,
      bestBacktest: { returnPercentage: 31.8, winRate: 65, totalTrades: 28, period: '2M' }
    }
  ];



  const [checkedItems, setCheckedItems] = useState<{[key: string]: boolean}>({
    'create-agent': false,
    'first-scan': false,
    'first-backtest': false,
    'portfolio-setup': false,
    'discover-agents': false,
    'make-agent-public': false,
    'setup-marketplace': false
  });

  // Load user data on component mount
  useEffect(() => {
    loadUserData();
  }, []);

  // Reload user data when user progress changes (with stable dependencies)
  const progressDeps = useMemo(() => [
    userProgress.hasCompletedFirstBacktest,
    userProgress.hasCompletedFirstScan,
    userProgress.hasCreatedFirstPortfolio,
    userProgress.hasVisitedDiscoverPage,
    userProgress.hasCreatedFirstPublicAgent,
    userProgress.scansCompleted,
    userProgress.stocksScanned,
    userProgress.backtestsCompleted,
    userProgress.portfoliosCreated
  ], [
    userProgress.hasCompletedFirstBacktest,
    userProgress.hasCompletedFirstScan,
    userProgress.hasCreatedFirstPortfolio,
    userProgress.hasVisitedDiscoverPage,
    userProgress.hasCreatedFirstPublicAgent,
    userProgress.scansCompleted,
    userProgress.stocksScanned,
    userProgress.backtestsCompleted,
    userProgress.portfoliosCreated
  ]);

  useEffect(() => {
    loadUserData();
  }, progressDeps);

  // Update checked items based on real progress
  useEffect(() => {
    setCheckedItems({
      'create-agent': userStats.agentsCreated > 0,
      'first-scan': userProgress.scansCompleted > 0,
      'first-backtest': userProgress.backtestsCompleted > 0,
      'portfolio-setup': userProgress.portfoliosCreated > 0,
      'discover-agents': userProgress.hasVisitedDiscoverPage,
      'make-agent-public': userProgress.hasCreatedFirstPublicAgent,
      'setup-marketplace': false // This will be updated when user sets up marketplace
    });
  }, [userStats, userProgress]);

  const loadUserData = async () => {
    try {
      setIsLoading(true);
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Load agent statistics
      const { data: agents } = await supabase
        .from('agents')
        .select('id, created_at')
        .eq('user_id', user.id);

      // Calculate time saved
      const portfolioCount = userProgress.portfoliosCreated;
      const scanTimeMinutes = userProgress.stocksScanned * 1;
      const backtestTimeMinutes = userProgress.backtestsCompleted * 60;
      const portfolioTimeMinutes = portfolioCount * 30;

      const totalTimeMinutes = scanTimeMinutes + backtestTimeMinutes + portfolioTimeMinutes;
      const timeSavedHours = Math.round(totalTimeMinutes / 60 * 10) / 10;

      // Get last activity date from agents
      const lastActivityDate = agents?.length > 0
        ? new Date(Math.max(...agents.map((a: any) => new Date(a.created_at).getTime()))).toISOString()
        : null;

      setUserStats({
        agentsCreated: agents?.length || 0,
        scansCompleted: userProgress.scansCompleted,
        backtestsCompleted: userProgress.backtestsCompleted,
        portfoliosCreated: userProgress.portfoliosCreated,
        winRate: 0,
        totalTrades: userProgress.tradesExecuted,
        successfulTrades: 0,
        timeSavedHours,
        lastActivityDate
      });
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Progress steps for get started journey
  const progressSteps: ProgressStep[] = [
    {
      id: 'create-agent',
      title: 'Create Agent',
      description: 'Build your first AI trading agent',
      icon: <Zap className="w-4 h-4" />,
      action: () => navigate('/agent-builder'),
      completed: checkedItems['create-agent']
    },
    {
      id: 'first-scan',
      title: 'Run Scan',
      description: 'Discover trading opportunities',
      icon: <Target className="w-4 h-4" />,
      action: () => navigate('/scanner'),
      completed: checkedItems['first-scan']
    },
    {
      id: 'first-backtest',
      title: 'Backtest Strategy',
      description: 'Test against historical data',
      icon: <BarChart3 className="w-4 h-4" />,
      action: () => navigate('/backtesting'),
      completed: checkedItems['first-backtest']
    },
    {
      id: 'discover-agents',
      title: 'Explore Marketplace',
      description: 'Discover community agents',
      icon: <Activity className="w-4 h-4" />,
      action: () => navigate('/marketplace'),
      completed: checkedItems['discover-agents']
    }
  ];

  const completedSteps = progressSteps.filter(step => step.completed).length;

  // Load user name
  useEffect(() => {
    const loadUserName = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user?.user_metadata?.full_name) {
          const firstName = user.user_metadata.full_name.split(' ')[0];
          setUserName(firstName);
        }
      } catch (error) {
        console.error('Error loading user name:', error);
      }
    };
    loadUserName();
  }, []);

  // Refresh watchlist when component mounts
  useEffect(() => {
    refreshWatchlist();
  }, [refreshWatchlist]);



  if (isLoading) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"></div>
          <p className="text-white/60">Loading your headquarters...</p>
        </div>
      </div>
    );
  }











  // Clean Earnings Card - No Graph
  const renderFixedEarningsCard = () => (
    <div className="h-full flex flex-col justify-center text-center">
      {/* Header */}
      <div className="mb-4">
        <h3 className="text-sm font-bold text-white mb-1">Total Earnings</h3>
        <p className="text-white/50 text-xs">This month</p>
      </div>

      {/* Amount and Percentage */}
      <div>
        <div className="text-3xl font-bold bg-gradient-to-r from-green-400 to-green-300 bg-clip-text text-transparent mb-2">
          $12,847
        </div>
        <div className="text-green-400 text-lg font-semibold">
          +18.4%
        </div>
      </div>
    </div>
  );

  // Simple Agents Card - Same style as Total Earnings
  const renderSimpleAgentsCard = () => {
    // Calculate average win rate from mock agents
    const avgWinRate = mockAgents.reduce((sum, agent) => sum + agent.winRate, 0) / mockAgents.length;

    return (
      <div className="h-full flex flex-col justify-center text-center">
        {/* Header */}
        <div className="mb-4">
          <h3 className="text-sm font-bold text-white mb-1">Your Agents</h3>
          <p className="text-white/50 text-xs">Active trading</p>
        </div>

        {/* Count and Win Rate */}
        <div>
          <div className="text-3xl font-bold bg-gradient-to-r from-green-400 to-green-300 bg-clip-text text-transparent mb-2">
            {mockAgents.length}
          </div>
          <div className="text-green-400 text-lg font-semibold">
            {avgWinRate.toFixed(1)}% Avg Win Rate
          </div>
        </div>
      </div>
    );
  };

  // Wide Get Started Progress Card - horizontal layout
  const renderExpandedGetStartedProgress = () => (
    <div className="h-full flex items-center gap-8">
      {/* Left side - Header and Progress */}
      <div className="flex-shrink-0 w-64">
        <div className="mb-4">
          <h3 className="text-xl font-bold text-white mb-2">Get Started</h3>
          <p className="text-white/50 text-sm">Complete your setup to unlock all features</p>
        </div>

        <div className="mb-4">
          <div className="flex items-center justify-between mb-3">
            <span className="text-white/70 text-sm">Progress</span>
            <span className="text-white font-semibold text-lg">{Math.round((completedSteps / progressSteps.length) * 100)}%</span>
          </div>
          <div className="w-full bg-white/[0.08] rounded-full h-3 overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-green-500 to-green-400 rounded-full transition-all duration-500"
              style={{ width: `${(completedSteps / progressSteps.length) * 100}%` }}
            />
          </div>
        </div>
      </div>

      {/* Right side - Checklist items in horizontal layout */}
      <div className="flex-1 grid grid-cols-3 gap-4">
        {progressSteps.map((step, index) => (
          <div key={index} className="flex items-center gap-3 p-4 bg-white/[0.02] rounded-lg border border-white/[0.05] hover:bg-white/[0.04] transition-all duration-200">
            <div className={`w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 ${
              step.completed
                ? 'bg-green-500/20 border border-green-500/40'
                : 'bg-white/[0.05] border border-white/[0.15]'
            }`}>
              {step.completed ? (
                <CheckCircle2 className="w-4 h-4 text-green-400" />
              ) : (
                <Circle className="w-4 h-4 text-white/40" />
              )}
            </div>
            <div className="flex-1 min-w-0">
              <div className={`text-sm font-medium ${step.completed ? 'text-white' : 'text-white/60'} truncate`}>
                {step.title}
              </div>
              <div className="text-white/40 text-xs truncate">
                {step.description}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  // Square Get Started Progress Card - Vertical layout
  const renderSquareGetStartedProgress = () => (
    <div className="h-full flex flex-col">
      <div className="mb-4">
        <h3 className="text-sm font-bold text-white mb-1">Get Started</h3>
        <p className="text-white/50 text-xs">Complete your setup</p>
      </div>

      <div className="flex-1 flex flex-col justify-center">
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-white/70 text-xs">Progress</span>
            <span className="text-white font-semibold text-xs">{Math.round((completedSteps / progressSteps.length) * 100)}%</span>
          </div>
          <div className="w-full bg-white/[0.08] rounded-full h-2 overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-green-500 to-green-400 rounded-full transition-all duration-500"
              style={{ width: `${(completedSteps / progressSteps.length) * 100}%` }}
            />
          </div>
        </div>

        <div className="space-y-2">
          {progressSteps.map((step, index) => (
            <div key={index} className="flex items-center gap-2 p-2 bg-white/[0.02] rounded border border-white/[0.05]">
              <div className={`w-4 h-4 rounded-full flex items-center justify-center ${
                step.completed
                  ? 'bg-green-500/20 border border-green-500/40'
                  : 'bg-white/[0.05] border border-white/[0.15]'
              }`}>
                {step.completed ? (
                  <CheckCircle2 className="w-2.5 h-2.5 text-green-400" />
                ) : (
                  <Circle className="w-2.5 h-2.5 text-white/40" />
                )}
              </div>
              <span className={`text-xs ${step.completed ? 'text-white' : 'text-white/60'}`}>
                {step.title}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  // Get Started Progress Card - Horizontal layout
  const renderGetStartedProgress = () => (
    <div className="h-full flex flex-col justify-center">
      <div className="mb-4">
        <h3 className="text-lg font-bold text-white mb-1">Get Started</h3>
        <p className="text-white/50 text-sm">Complete your setup journey</p>
      </div>

      <div className="flex items-center justify-between gap-4 mb-4">
        {progressSteps.map((step, index) => {
          const isCompleted = index < completedSteps;
          const isCurrent = index === completedSteps;

          return (
            <div key={step.id} className="flex flex-col items-center text-center flex-1">
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center border transition-all duration-200 mb-2 ${
                isCompleted
                  ? 'bg-green-500/20 border-green-500/40 text-green-400'
                  : isCurrent
                  ? 'bg-blue-500/20 border-blue-500/40 text-blue-400'
                  : 'bg-white/[0.05] border-white/[0.15] text-white/40'
              }`}>
                {isCompleted ? (
                  <span className="text-sm">✓</span>
                ) : (
                  <span className="text-sm">{index + 1}</span>
                )}
              </div>

              <div className={`text-xs font-medium ${
                isCompleted ? 'text-white' : isCurrent ? 'text-white' : 'text-white/50'
              }`}>
                {step.title}
              </div>
            </div>
          );
        })}
      </div>

      <div className="flex items-center justify-between">
        <div className="flex-1 h-2 bg-white/[0.08] rounded-full overflow-hidden">
          <motion.div
            className="h-full bg-green-400 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${(completedSteps / progressSteps.length) * 100}%` }}
            transition={{ duration: 1.5, ease: "easeOut" }}
          />
        </div>
        <div className="ml-4 text-right">
          <div className="text-lg font-bold text-green-400">
            {Math.round((completedSteps / progressSteps.length) * 100)}%
          </div>
          <div className="text-white/40 text-xs">Complete</div>
        </div>
      </div>
    </div>
  );





  // Real Candlestick Chart Component
  const RealCandlestickChart = ({ symbol }: { symbol: string }) => {
    const { data: chartData, isLoading } = useQuery({
      queryKey: ['watchlistChart', symbol],
      queryFn: () => fetchPolygonChartData({ symbol, timeframe: '1D' }),
      enabled: !!symbol,
      staleTime: 5 * 60 * 1000, // 5 minutes
    });

    if (isLoading || !chartData?.data) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-white/40 text-xs">Loading...</div>
        </div>
      );
    }

    const formattedData = chartData.data.slice(-10).map((item: any) => [
      item.open,
      item.close,
      item.low,
      item.high
    ]);

    const option = {
      animation: false,
      grid: {
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        containLabel: false
      },
      xAxis: {
        type: 'category',
        show: false,
        data: chartData.data.slice(-10).map((_: any, index: number) => index)
      },
      yAxis: {
        type: 'value',
        show: false,
        scale: true
      },
      series: [{
        type: 'candlestick',
        data: formattedData,
        itemStyle: {
          color: '#10b981', // Green for bullish
          color0: '#ef4444', // Red for bearish
          borderColor: '#10b981',
          borderColor0: '#ef4444',
          borderWidth: 1
        },
        barWidth: '60%'
      }]
    };

    return (
      <ReactECharts
        option={option}
        style={{ height: '100%', width: '100%' }}
        opts={{ renderer: 'svg' }}
      />
    );
  };

  // TradingView Style Watchlist - Vertical Splits with Real Charts
  const renderHorizontalWatchlistCard = () => {
    // Real stock data
    const testWatchlistData = [
      {
        symbol: 'AAPL',
        name: 'Apple Inc.',
        price: 193.42,
        change: 2.87,
        changePercent: 1.51
      },
      {
        symbol: 'MSFT',
        name: 'Microsoft Corp.',
        price: 415.26,
        change: -2.14,
        changePercent: -0.51
      },
      {
        symbol: 'GOOGL',
        name: 'Alphabet Inc.',
        price: 175.84,
        change: 4.23,
        changePercent: 2.47
      }
    ];

    return (
      <div className="h-full flex flex-col">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-bold text-white">Watchlist</h3>
          <div className="text-white/50 text-sm">{testWatchlistData.length} stocks</div>
        </div>

        {/* TradingView Style - Vertical Splits */}
        <div className="flex-1 flex gap-0">
          {testWatchlistData.map((stock, index) => {
            const isPositive = stock.change >= 0;

            return (
              <div key={stock.symbol} className={`flex-1 flex flex-col ${index < testWatchlistData.length - 1 ? 'border-r border-white/[0.08]' : ''}`}>
                {/* Ticker and Price at top */}
                <div className="p-3 border-b border-white/[0.05]">
                  <div className="font-bold text-white text-sm">{stock.symbol}</div>
                  <div className="text-white font-semibold text-xs">${stock.price.toFixed(2)}</div>
                  <div className={`text-xs font-medium ${isPositive ? 'text-green-400' : 'text-red-400'}`}>
                    {isPositive ? '+' : ''}{stock.changePercent.toFixed(2)}%
                  </div>
                </div>

                {/* Real Candlestick Chart */}
                <div className="flex-1 px-2 pb-2">
                  <RealCandlestickChart symbol={stock.symbol} />
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // Square Watchlist Card - Vertical Layout
  const renderSquareWatchlistCard = () => {
    return (
      <div className="h-full flex flex-col">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-bold text-white">Watchlist</h3>
          <div className="text-white/50 text-sm">{watchlistStocks.length} stocks</div>
        </div>

        <div className="flex-1 overflow-hidden">
          {watchlistStocks.length === 0 ? (
            <div className="h-full flex flex-col items-center justify-center text-center">
              <div className="w-12 h-12 bg-white/[0.05] rounded-xl flex items-center justify-center mb-3 border border-white/[0.08]">
                <TrendingUp className="w-6 h-6 text-white/40" />
              </div>
              <p className="text-white/50 text-sm mb-2">No stocks in watchlist</p>
              <p className="text-white/30 text-xs">Add stocks from the screener</p>
            </div>
          ) : (
            <div className="space-y-3 h-full overflow-y-auto">
              {watchlistStocks.slice(0, 6).map((stock, index) => (
                <div key={stock.id || index} className="flex items-center justify-between p-3 bg-white/[0.02] rounded-lg border border-white/[0.05] hover:bg-white/[0.04] transition-all duration-200">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-white font-semibold text-sm">{stock.symbol}</span>
                      <span className="text-white text-sm font-bold">${stock.price?.toFixed(2) || '0.00'}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/50 text-xs">{stock.name}</span>
                      <span className={`text-xs font-semibold ${(stock.change || 0) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {(stock.change || 0) >= 0 ? '+' : ''}{(stock.changePercent || 0).toFixed(2)}%
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  // Enhanced Watchlist Card - Row Layout
  const renderEnhancedWatchlistCard = () => {
    return (
      <div className="h-full flex flex-col">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-bold text-white mb-1">Watchlist</h3>
            <p className="text-white/50 text-sm">Your tracked stocks</p>
          </div>
          <button
            onClick={() => navigate('/stock-screener')}
            className="text-white/60 hover:text-white text-xs font-medium transition-all duration-200"
          >
            Scanner →
          </button>
        </div>

        <div className="flex-1">
          {watchlistStocks.length === 0 ? (
            <div className="h-full flex flex-col items-center justify-center">
              <div className="text-center">
                <Target className="w-8 h-8 text-white/30 mx-auto mb-3" />
                <p className="text-white/50 text-sm mb-3">No stocks in watchlist</p>
                <button
                  onClick={() => navigate('/stock-screener')}
                  className="text-white/70 hover:text-white text-xs font-medium transition-all duration-200 bg-white/[0.05] px-3 py-2 rounded-lg border border-white/[0.10] hover:bg-white/[0.08]"
                >
                  Browse Stocks
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              {watchlistStocks.slice(0, 6).map((stock) => {
                // Use actual stock data
                const price = stock.price || 0;
                const change = stock.change || 0;
                const changePercent = stock.changePercent || 0;
                const isPositive = change >= 0;

                return (
                  <div
                    key={stock.id}
                    className="flex items-center justify-between py-3 px-4 bg-white/[0.02] rounded-xl border border-white/[0.05] hover:bg-white/[0.04] hover:border-white/[0.08] transition-all duration-200 cursor-pointer group"
                    onClick={() => navigate(`/stock-screener/${stock.symbol}`)}
                  >
                    {/* Left: Symbol & Chart */}
                    <div className="flex items-center gap-4">
                      <div>
                        <div className="font-bold text-white text-sm">{stock.symbol}</div>
                        <div className="text-white/50 text-xs">{stock.name || `${stock.symbol} Inc.`}</div>
                      </div>

                      {/* Mini Line Chart */}
                      <div className="w-16 h-6">
                        <svg width="100%" height="100%" viewBox="0 0 64 24" preserveAspectRatio="none">
                          <defs>
                            <linearGradient id={`gradient-${stock.id}`} x1="0%" y1="0%" x2="100%" y2="0%">
                              <stop offset="0%" stopColor={isPositive ? '#10b981' : '#ef4444'} stopOpacity="0.8" />
                              <stop offset="100%" stopColor={isPositive ? '#34d399' : '#f87171'} stopOpacity="0.6" />
                            </linearGradient>
                          </defs>
                          <path
                            d={`M0,12 ${Array.from({length: 12}, (_, i) => {
                              const x = (i / 11) * 64;
                              const baseY = 12;
                              const trend = isPositive ? -1 : 1;
                              const noise = (Math.random() - 0.5) * 4;
                              const trendEffect = (i / 11) * trend * 6;
                              const y = baseY + trendEffect + noise;
                              return `L${x},${Math.max(2, Math.min(22, y))}`;
                            }).join(' ')}`}
                            fill="none"
                            stroke={`url(#gradient-${stock.id})`}
                            strokeWidth="2"
                          />
                        </svg>
                      </div>
                    </div>

                    {/* Right: Price & Change */}
                    <div className="text-right">
                      <div className="text-white text-sm font-medium">${price.toFixed(2)}</div>
                      <div className={`text-xs font-medium ${isPositive ? 'text-green-400' : 'text-red-400'}`}>
                        {isPositive ? '+' : ''}{changePercent.toFixed(2)}%
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    );
  };

  // Enhanced Agents Card with more information
  const renderEnhancedAgentsCard = () => {
    // Sort agents by win rate (highest first)
    const sortedAgents = [...mockAgents].sort((a, b) => b.winRate - a.winRate);

    return (
      <div className="h-full flex flex-col">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-bold text-white mb-1">Your Agents</h3>
            <p className="text-white/50 text-xs">AI trading strategies ranked by performance</p>
          </div>
          <div className="text-right">
            <div className="text-green-400 text-lg font-bold">+12.4%</div>
            <div className="text-white/50 text-xs">Total Return</div>
          </div>
        </div>

        <div className="flex-1 overflow-hidden">
          {mockAgents.length === 0 ? (
            <div className="h-full flex items-center justify-center">
              <div className="text-center">
                <Activity className="w-8 h-8 text-white/30 mx-auto mb-3" />
                <h4 className="text-white/60 text-base font-medium mb-2">No agents created yet</h4>
                <p className="text-white/40 text-sm mb-4">Create your first AI trading agent</p>
                <button
                  onClick={() => navigate('/builder')}
                  className="bg-white/[0.05] border border-white/[0.10] text-white text-sm font-medium py-2 px-4 rounded-lg hover:bg-white/[0.08] hover:border-white/[0.15] transition-all duration-200"
                >
                  Create Agent
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-3 h-full overflow-y-auto">
              {sortedAgents.map((agent, index) => (
                <div
                  key={agent.id}
                  className="flex items-center justify-between py-3 px-4 bg-white/[0.02] rounded-lg border border-white/[0.05] hover:bg-white/[0.04] hover:border-white/[0.08] transition-all duration-200 cursor-pointer"
                  onClick={() => navigate('/builder')}
                >
                  {/* Left Side - Rank & Name */}
                  <div className="flex items-center gap-4 flex-1">
                    <div className="w-8 h-8 bg-gradient-to-br from-green-400/20 to-green-600/20 rounded-lg flex items-center justify-center border border-green-500/30">
                      <span className="text-green-400 text-xs font-bold">#{index + 1}</span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-semibold text-white text-sm truncate">{agent.name}</div>
                      <div className="text-white/50 text-xs truncate">{agent.description}</div>
                    </div>
                  </div>

                  {/* Right Side - Stats */}
                  <div className="flex items-center gap-4 text-right">
                    <div>
                      <div className="text-white font-semibold text-sm">{agent.winRate}%</div>
                      <div className="text-white/40 text-xs">Win Rate</div>
                    </div>
                    <div>
                      <div className={`text-sm font-bold ${
                        agent.returnPercentage >= 0 ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {agent.returnPercentage >= 0 ? '+' : ''}{agent.returnPercentage}%
                      </div>
                      <div className="text-white/40 text-xs">Return</div>
                    </div>
                    <div>
                      <div className="text-white font-semibold text-sm">{agent.totalTrades}</div>
                      <div className="text-white/40 text-xs">Trades</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Action Button */}
        <button
          onClick={() => navigate('/builder')}
          className="w-full mt-4 py-2 text-white/70 hover:text-white text-sm font-medium transition-all duration-200"
        >
          Manage All Agents
        </button>
      </div>
    );
  };

  // Clean Row-Based Agents Card
  const renderCleanRowAgentsCard = () => {
    // Sort agents by win rate (highest first)
    const sortedAgents = [...mockAgents].sort((a, b) => b.winRate - a.winRate);

    return (
      <div className="h-full flex flex-col">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-bold text-white mb-1">Your Agents</h3>
            <p className="text-white/50 text-xs">AI trading strategies</p>
          </div>
          <div className="text-right">
            <div className="text-green-400 text-lg font-bold">+12.4%</div>
            <div className="text-white/50 text-xs">Total Return</div>
          </div>
        </div>

        {/* Agents List - Clean Rows */}
        <div className="flex-1 space-y-2">
          {sortedAgents.map((agent, index) => (
            <div
              key={agent.id}
              className="flex items-center justify-between py-3 px-4 bg-white/[0.02] rounded-lg border border-white/[0.05] hover:bg-white/[0.04] hover:border-white/[0.08] transition-all duration-200 cursor-pointer"
              onClick={() => navigate('/builder')}
            >
              {/* Left Side - Rank & Name */}
              <div className="flex items-center gap-4">
                <div className="text-white font-bold text-lg min-w-[24px]">#{index + 1}</div>
                <div>
                  <div className="font-semibold text-white text-sm">{agent.name}</div>
                  <div className="text-white/50 text-xs">{agent.description}</div>
                </div>
              </div>

              {/* Right Side - Stats */}
              <div className="flex items-center gap-6 text-right">
                <div>
                  <div className="text-white font-semibold text-sm">{agent.winRate}%</div>
                  <div className="text-white/40 text-xs">Win Rate</div>
                </div>
                <div>
                  <div className={`text-sm font-bold ${
                    agent.returnPercentage >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {agent.returnPercentage >= 0 ? '+' : ''}{agent.returnPercentage}%
                  </div>
                  <div className="text-white/40 text-xs">Return</div>
                </div>
                <div>
                  <div className="text-white font-semibold text-sm">{agent.totalTrades}</div>
                  <div className="text-white/40 text-xs">Trades</div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {mockAgents.length === 0 && (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <Activity className="w-8 h-8 text-white/30 mx-auto mb-3" />
              <h4 className="text-white/60 text-base font-medium mb-2">No agents created yet</h4>
              <p className="text-white/40 text-sm mb-4">Create your first AI trading agent</p>
              <button
                onClick={() => navigate('/builder')}
                className="bg-white/[0.05] border border-white/[0.10] text-white text-sm font-medium py-2 px-4 rounded-lg hover:bg-white/[0.08] hover:border-white/[0.15] transition-all duration-200"
              >
                Create Agent
              </button>
            </div>
          </div>
        )}

        {/* Action Button */}
        <button
          onClick={() => navigate('/builder')}
          className="w-full mt-4 py-2 text-white/70 hover:text-white text-sm font-medium transition-all duration-200"
        >
          Manage All Agents
        </button>
      </div>
    );
  };



  // Compact Market Overview Card - fits in smaller space
  const renderCompactMarketOverviewCard = () => (
    <div className="h-full flex flex-col">
      <div className="mb-3">
        <h3 className="text-base font-bold text-white mb-1">Market</h3>
        <p className="text-white/50 text-xs">Today's performance</p>
      </div>

      <div className="flex-1 flex flex-col justify-between">
        {/* Market Indices */}
        <div className="space-y-2">
          {[
            { name: 'S&P 500', change: '+1.2%', positive: true },
            { name: 'NASDAQ', change: '+0.8%', positive: true },
            { name: 'DOW', change: '-0.3%', positive: false }
          ].map((index) => (
            <div key={index.name} className="flex items-center justify-between py-1.5 px-2 bg-white/[0.02] rounded-lg border border-white/[0.05]">
              <div className="font-semibold text-white text-xs">{index.name}</div>
              <div className={`text-xs font-medium ${index.positive ? 'text-green-400' : 'text-red-400'}`}>
                {index.change}
              </div>
            </div>
          ))}
        </div>

        {/* Market Status */}
        <div className="pt-2 border-t border-white/[0.05]">
          <div className="flex items-center justify-between">
            <span className="text-white/50 text-xs">Status</span>
            <div className="flex items-center gap-1">
              <div className="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
              <span className="text-green-400 text-xs font-medium">Open</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white">
      {/* Clean Compact Header */}
      <div className="px-8 py-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white" style={{ fontFamily: 'SF Pro Display, -apple-system, BlinkMacSystemFont, sans-serif' }}>
              Welcome to HQ, <span className="bg-gradient-to-r from-gray-200 to-white bg-clip-text text-transparent">{userName}</span>
            </h1>
          </div>

          {/* Setup Progress - Compact */}
          <div className="flex items-center gap-3">
            <div className="text-right">
              <div className="text-lg font-bold text-green-400">
                {Math.round((completedSteps / progressSteps.length) * 100)}%
              </div>
              <div className="text-white/40 text-xs">Setup Complete</div>
            </div>

            {/* Mini Progress Bar */}
            <div className="relative w-24 h-1 bg-white/[0.08] rounded-full overflow-hidden">
              <motion.div
                className="absolute top-0 left-0 h-full bg-green-400 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${(completedSteps / progressSteps.length) * 100}%` }}
                transition={{ duration: 1.5, ease: "easeOut" }}
              />
            </div>
          </div>
        </div>
      </div>



      {/* Premium Bento Dashboard */}
      <div className="p-8">
        <div className="max-w-7xl mx-auto">
          {/* Perfect Rectangle Bento Grid Layout */}
          <div className="grid grid-cols-4 grid-rows-4 gap-6 h-[900px]">

            {/* Total Earnings Card - Row 1, Left */}
            <div className="col-span-1 row-span-1 bg-[#0F0F0F] backdrop-blur-sm border border-white/[0.08] rounded-2xl p-6 transition-all duration-300 hover:bg-[#111111] hover:border-white/[0.12] shadow-2xl">
              {renderFixedEarningsCard()}
            </div>

            {/* Watchlist Card - Row 1-2, Right side, taller */}
            <div className="col-span-2 row-span-2 bg-[#0F0F0F] backdrop-blur-sm border border-white/[0.08] rounded-2xl p-6 transition-all duration-300 hover:bg-[#111111] hover:border-white/[0.12] shadow-2xl">
              {renderHorizontalWatchlistCard()}
            </div>

            {/* Empty space - Row 1, Far Right */}
            <div className="col-span-1 row-span-1"></div>

            {/* Your Agents Card - Row 2, Left (below Total Earnings) */}
            <div className="col-span-1 row-span-1 bg-[#0F0F0F] backdrop-blur-sm border border-white/[0.08] rounded-2xl p-6 transition-all duration-300 hover:bg-[#111111] hover:border-white/[0.12] shadow-2xl">
              {renderSimpleAgentsCard()}
            </div>

            {/* Empty space - Row 2, Far Right */}
            <div className="col-span-1 row-span-1"></div>

            {/* Empty spaces - Row 3-4 */}
            <div className="col-span-4 row-span-2"></div>

          </div>

          {/* Get Started Progress - Below bento grid */}
          <div className="mt-6 bg-[#0F0F0F] backdrop-blur-sm border border-white/[0.08] rounded-2xl p-6 transition-all duration-300 hover:bg-[#111111] hover:border-white/[0.12] shadow-2xl">
            {renderExpandedGetStartedProgress()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
