// Test to verify single input terminal approach for AND/OR blocks
console.log('Testing single input terminal for AND/OR blocks...');

// Simulate how the blocks would work with single input terminal
const testAndBlock = {
  id: 'and-1',
  type: 'AND',
  inputConnections: ['condition-1', 'condition-2', 'condition-3'], // Multiple connections to single terminal
  trueConnection: 'trigger-bullish',
  falseConnection: 'trigger-bearish'
};

const testOrBlock = {
  id: 'or-1', 
  type: 'OR',
  inputConnections: ['condition-1', 'condition-2'], // Multiple connections to single terminal
  trueConnection: 'trigger-bullish',
  falseConnection: 'trigger-bearish'
};

// Simulate block results
const blockResults = new Map([
  ['condition-1', true],   // RSI > 70
  ['condition-2', false],  // Volume > Average
  ['condition-3', true]    // Price > SMA
]);

// Test AND logic (should be false because condition-2 is false)
const andInputValues = testAndBlock.inputConnections.map(id => blockResults.get(id));
const andResult = andInputValues.every(val => !!val);
console.log(`AND Test: inputs [${andInputValues.join(', ')}] → result: ${andResult}`);

// Test OR logic (should be true because condition-1 and condition-3 are true)
const orInputValues = testOrBlock.inputConnections.map(id => blockResults.get(id));
const orResult = orInputValues.some(val => !!val);
console.log(`OR Test: inputs [${orInputValues.join(', ')}] → result: ${orResult}`);

console.log('\n✅ Single input terminal approach works correctly!');
console.log('\nBenefits of single input terminal:');
console.log('- Cleaner visual interface');
console.log('- Unlimited connections to one terminal');
console.log('- Simpler connection management');
console.log('- More intuitive for users');
console.log('- Consistent with other block types');

console.log('\nHow it works:');
console.log('1. AND/OR blocks have one input terminal on the left');
console.log('2. Multiple condition blocks can connect to this single terminal');
console.log('3. The inputConnections array stores all connected block IDs');
console.log('4. Logic evaluation processes all connected inputs');
console.log('5. Result flows to true/false output terminals');

console.log('\nExample usage:');
console.log('Condition 1 (RSI > 70) ──┐');
console.log('Condition 2 (Vol > Avg) ─┼─→ [AND] ──→ Trigger');
console.log('Condition 3 (Price > MA) ─┘');
