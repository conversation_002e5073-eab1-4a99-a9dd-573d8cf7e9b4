// Test the comparison logic that's failing in the agent-runner
console.log('Testing comparison logic...');

// Mock the comparison function from agent-executor.ts
function executeComparisonBlock(block, blockResults, polygonData) {
  const { operator, inputConnections } = block;

  console.log(`Executing comparison block with operator: ${operator}`);
  console.log(`Input connections: ${inputConnections ? inputConnections.join(', ') : 'none'}`);

  // Comparison blocks require exactly 2 inputs
  if (!inputConnections || inputConnections.length !== 2) {
    throw new Error(`Comparison block requires exactly 2 input connections, got ${inputConnections?.length || 0}`);
  }

  // Get values from both input blocks
  const valueA = blockResults.get(inputConnections[0]);
  const valueB = blockResults.get(inputConnections[1]);

  if (valueA === undefined) {
    throw new Error(`Block result not found for input A (ID: ${inputConnections[0]})`);
  }
  if (valueB === undefined) {
    throw new Error(`Block result not found for input B (ID: ${inputConnections[1]})`);
  }

  console.log(`Comparing: A=${valueA} ${operator} B=${valueB}`);

  // Convert values to numbers for comparison
  let numericValueA = Number(valueA);
  let numericValueB = Number(valueB);

  if (isNaN(numericValueA)) {
    console.warn(`Value A is not a number: ${valueA}, attempting to convert`);
    numericValueA = 0;
  }
  if (isNaN(numericValueB)) {
    console.warn(`Value B is not a number: ${valueB}, attempting to convert`);
    numericValueB = 0;
  }

  console.log(`Numeric comparison: A=${numericValueA} ${operator} B=${numericValueB}`);

  // Perform the comparison
  let result;
  switch (operator) {
    case '>':
      result = numericValueA > numericValueB;
      break;
    case '<':
      result = numericValueA < numericValueB;
      break;
    case '>=':
      result = numericValueA >= numericValueB;
      break;
    case '<=':
      result = numericValueA <= numericValueB;
      break;
    case '==':
      result = numericValueA === numericValueB;
      break;
    case '!=':
      result = numericValueA !== numericValueB;
      break;
    default:
      throw new Error(`Unknown comparison operator: ${operator}`);
  }

  console.log(`Comparison result: ${result}`);
  return result;
}

// Test with the exact values from your logs
const mockBlockResults = new Map();
mockBlockResults.set('c7ca547f-c18d-4d80-a503-bb60b8baf370', 200.6724643488833); // VWAP
mockBlockResults.set('e8f816b3-3c1f-478a-85b5-ce9b7680ff52', 201.08); // Price

const mockComparisonBlock = {
  operator: '<',
  inputConnections: ['c7ca547f-c18d-4d80-a503-bb60b8baf370', 'e8f816b3-3c1f-478a-85b5-ce9b7680ff52']
};

console.log('\n🧪 Testing comparison with exact values from logs...\n');

try {
  const result = executeComparisonBlock(mockComparisonBlock, mockBlockResults, null);
  console.log(`\n✅ Comparison executed successfully!`);
  console.log(`📊 Final result: ${result}`);
  console.log(`📝 Interpretation: VWAP (200.67) < Price (201.08) = ${result}`);
} catch (error) {
  console.log(`\n❌ Comparison failed with error:`);
  console.error(error.message);
  console.error(error.stack);
}

// Test edge cases
console.log('\n🔬 Testing edge cases...\n');

const edgeCases = [
  {
    name: 'Equal values',
    valueA: 100,
    valueB: 100,
    operator: '==',
    expected: true
  },
  {
    name: 'String numbers',
    valueA: '100.5',
    valueB: '200.3',
    operator: '<',
    expected: true
  },
  {
    name: 'Zero values',
    valueA: 0,
    valueB: 1,
    operator: '<',
    expected: true
  },
  {
    name: 'Negative values',
    valueA: -10,
    valueB: 5,
    operator: '<',
    expected: true
  }
];

edgeCases.forEach(({ name, valueA, valueB, operator, expected }) => {
  const testBlockResults = new Map();
  testBlockResults.set('input1', valueA);
  testBlockResults.set('input2', valueB);
  
  const testBlock = {
    operator,
    inputConnections: ['input1', 'input2']
  };
  
  try {
    const result = executeComparisonBlock(testBlock, testBlockResults, null);
    const passed = result === expected;
    console.log(`${passed ? '✅' : '❌'} ${name}: ${valueA} ${operator} ${valueB} = ${result} (expected: ${expected})`);
  } catch (error) {
    console.log(`❌ ${name}: Error - ${error.message}`);
  }
});

console.log('\n✨ Test completed!');
