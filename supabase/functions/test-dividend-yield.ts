// Test script for dividend yield functionality in fundamentals block
import { executeAgent } from "./agent-runner/agent-executor.ts";
import { BlockType } from "./agent-runner/agent-types.ts";

// Test configuration with dividend yield fundamental block
const testDividendYieldAgent = {
  id: "test-dividend-yield-agent",
  name: "Dividend Yield Test Agent",
  blocks: [
    // Entry point
    {
      id: "when-run-1",
      type: BlockType.WHEN_RUN,
      x: 100,
      y: 100,
      outputConnections: ["dividend-yield-1"]
    },
    
    // Dividend yield fundamental block - explicitly set statement to market_fundamentals
    {
      id: "dividend-yield-1",
      type: BlockType.FUNDAMENTAL,
      metric: "dividend_yield",
      statement: "market_fundamentals",
      x: 200,
      y: 100,
      outputConnections: ["condition-1"]
    },
    
    // Condition to check if dividend yield > 3%
    {
      id: "condition-1",
      type: BlockType.CONDITION,
      operator: ">",
      compareValue: 3,
      x: 300,
      y: 100,
      inputConnections: ["dividend-yield-1"],
      trueConnection: "signal-bullish",
      falseConnection: "signal-neutral"
    },
    
    // Bullish signal for high dividend yield
    {
      id: "signal-bullish",
      type: BlockType.SIGNAL,
      signal: "bullish",
      confidence: 75,
      x: 400,
      y: 50,
      inputConnections: ["condition-1"]
    },
    
    // Neutral signal for low dividend yield
    {
      id: "signal-neutral",
      type: BlockType.SIGNAL,
      signal: "neutral",
      confidence: 50,
      x: 400,
      y: 150,
      inputConnections: ["condition-1"]
    }
  ]
};

// Mock polygon data with dividend yield (calculated from dividend API)
const mockPolygonDataWithDividend = {
  symbol: "AAPL",
  price: {
    current: 150.00,
    open: 148.50,
    high: 151.20,
    low: 147.80,
    close: 150.00,
    volume: 1000000,
    timestamp: Date.now()
  },
  historical: {
    open: Array(50).fill(0).map((_, i) => 148 + Math.random() * 4),
    high: Array(50).fill(0).map((_, i) => 149 + Math.random() * 4),
    low: Array(50).fill(0).map((_, i) => 147 + Math.random() * 4),
    close: Array(50).fill(0).map((_, i) => 148 + Math.random() * 4),
    volume: Array(50).fill(0).map(() => 800000 + Math.random() * 400000),
    timestamp: Array(50).fill(0).map((_, i) => Date.now() - (49 - i) * 24 * 60 * 60 * 1000)
  },
  indicators: {
    rsi: Array(50).fill(0).map(() => 20 + Math.random() * 60)
  },
  fundamentals: {
    // Dividend yield calculated from actual dividend data
    // Example: $6.30 annual dividend / $150 current price = 4.2%
    dividendYield: 4.2, // 4.2% dividend yield - should trigger bullish signal
    peRatio: 25.5,
    pbRatio: 3.2,
    marketCap: 2500000000000
  },
  financials: {}
};

// Test function for dividend yield
async function testDividendYield() {
  console.log("🧪 Testing Dividend Yield Fundamental Block");
  console.log("=" .repeat(50));
  
  try {
    console.log("📊 Executing dividend yield test agent...");
    console.log(`📈 Test data: AAPL with ${mockPolygonDataWithDividend.fundamentals.dividendYield}% dividend yield`);
    
    const result = await executeAgent(testDividendYieldAgent, mockPolygonDataWithDividend);
    
    console.log("\n✅ Agent execution completed successfully!");
    console.log("📈 Result Summary:");
    console.log(`   Symbol: ${mockPolygonDataWithDividend.symbol}`);
    console.log(`   Dividend Yield: ${mockPolygonDataWithDividend.fundamentals.dividendYield}%`);
    console.log(`   Signal: ${result.signal}`);
    console.log(`   Confidence: ${result.confidence}%`);
    
    // Verify the result
    const expectedSignal = mockPolygonDataWithDividend.fundamentals.dividendYield > 3 ? 'bullish' : 'neutral';
    const isCorrect = result.signal === expectedSignal;
    
    console.log("\n🎯 Test Results:");
    console.log(`   Expected Signal: ${expectedSignal}`);
    console.log(`   Actual Signal: ${result.signal}`);
    console.log(`   Test Result: ${isCorrect ? '✅ PASSED' : '❌ FAILED'}`);
    
    if (isCorrect) {
      console.log("   ✅ Dividend yield fundamental block working correctly");
      console.log("   ✅ Market fundamentals data accessed successfully");
      console.log("   ✅ Condition logic executed properly");
    } else {
      console.log("   ❌ Unexpected signal generated");
    }
    
    return isCorrect;
    
  } catch (error) {
    console.error("\n❌ Test failed with error:");
    console.error(error);
    return false;
  }
}

// Test with incorrect statement configuration (simulating the bug)
async function testIncorrectStatementConfig() {
  console.log("\n🧪 Testing Incorrect Statement Configuration (Bug Simulation)");
  console.log("=" .repeat(50));

  const buggyAgentConfig = {
    id: "test-buggy-dividend-yield-agent",
    name: "Buggy Dividend Yield Test Agent",
    blocks: [
      {
        id: "when-run-1",
        type: BlockType.WHEN_RUN,
        x: 100,
        y: 100,
        outputConnections: ["dividend-yield-1"]
      },
      // Simulate the bug - dividend_yield with wrong or missing statement
      {
        id: "dividend-yield-1",
        type: BlockType.FUNDAMENTAL,
        metric: "dividend_yield",
        // statement: "calculated", // This would cause the error
        // statement: undefined, // This would also cause the error
        x: 200,
        y: 100,
        outputConnections: ["condition-1"]
      },
      {
        id: "condition-1",
        type: BlockType.CONDITION,
        operator: ">",
        compareValue: 3,
        x: 300,
        y: 100,
        inputConnections: ["dividend-yield-1"],
        trueConnection: "signal-bullish",
        falseConnection: "signal-neutral"
      },
      {
        id: "signal-bullish",
        type: BlockType.SIGNAL,
        signal: "bullish",
        confidence: 75,
        x: 400,
        y: 50,
        inputConnections: ["condition-1"]
      },
      {
        id: "signal-neutral",
        type: BlockType.SIGNAL,
        signal: "neutral",
        confidence: 50,
        x: 400,
        y: 150,
        inputConnections: ["condition-1"]
      }
    ]
  };

  try {
    console.log("📊 Executing agent with incorrect statement configuration...");
    console.log(`📈 Test data: AAPL with ${mockPolygonDataWithDividend.fundamentals.dividendYield}% dividend yield`);

    const result = await executeAgent(buggyAgentConfig, mockPolygonDataWithDividend);

    console.log("\n✅ Agent execution completed successfully!");
    console.log("📈 Result Summary:");
    console.log(`   Symbol: ${mockPolygonDataWithDividend.symbol}`);
    console.log(`   Dividend Yield: ${mockPolygonDataWithDividend.fundamentals.dividendYield}%`);
    console.log(`   Signal: ${result.signal}`);
    console.log(`   Confidence: ${result.confidence}%`);

    // Verify the result
    const expectedSignal = mockPolygonDataWithDividend.fundamentals.dividendYield > 3 ? 'bullish' : 'neutral';
    const isCorrect = result.signal === expectedSignal;

    console.log("\n🎯 Bug Fix Test Results:");
    console.log(`   Expected Signal: ${expectedSignal}`);
    console.log(`   Actual Signal: ${result.signal}`);
    console.log(`   Bug Fix Working: ${isCorrect ? '✅ YES' : '❌ NO'}`);

    if (isCorrect) {
      console.log("   ✅ Auto-correction logic working properly");
      console.log("   ✅ Dividend yield accessed from fundamentals data");
      console.log("   ✅ Error handling prevented crash");
    } else {
      console.log("   ❌ Bug fix not working as expected");
    }

    return isCorrect;

  } catch (error) {
    console.error("\n❌ Bug fix test failed with error:");
    console.error(error);
    return false;
  }
}

// Test with no dividend yield (non-dividend paying stock)
async function testNoDividendYield() {
  console.log("\n🧪 Testing No Dividend Yield (Non-Dividend Paying Stock)");
  console.log("=" .repeat(50));

  const noDividendData = {
    ...mockPolygonDataWithDividend,
    fundamentals: {
      ...mockPolygonDataWithDividend.fundamentals,
      dividendYield: null // No dividend yield - should return 0 and trigger neutral signal
    }
  };

  try {
    console.log("📊 Executing agent with non-dividend paying stock...");
    console.log(`📈 Test data: AAPL with no dividend yield (null)`);

    const result = await executeAgent(testDividendYieldAgent, noDividendData);

    console.log("\n✅ Agent execution completed successfully!");
    console.log("📈 Result Summary:");
    console.log(`   Symbol: ${noDividendData.symbol}`);
    console.log(`   Dividend Yield: ${noDividendData.fundamentals.dividendYield} (null/undefined)`);
    console.log(`   Signal: ${result.signal}`);
    console.log(`   Confidence: ${result.confidence}%`);

    // For non-dividend paying stocks, should return neutral signal (0% < 3%)
    const expectedSignal = 'neutral';
    const isCorrect = result.signal === expectedSignal;

    console.log("\n🎯 No Dividend Test Results:");
    console.log(`   Expected Signal: ${expectedSignal}`);
    console.log(`   Actual Signal: ${result.signal}`);
    console.log(`   Test Result: ${isCorrect ? '✅ PASSED' : '❌ FAILED'}`);

    if (isCorrect) {
      console.log("   ✅ Non-dividend paying stocks handled correctly");
      console.log("   ✅ Null dividend yield converted to 0%");
      console.log("   ✅ Condition logic executed properly");
    } else {
      console.log("   ❌ Non-dividend paying stock handling failed");
    }

    return isCorrect;

  } catch (error) {
    console.error("\n❌ No dividend test failed with error:");
    console.error(error);
    return false;
  }
}

// Test with low dividend yield
async function testLowDividendYield() {
  console.log("\n🧪 Testing Low Dividend Yield Scenario");
  console.log("=" .repeat(50));

  const lowDividendData = {
    ...mockPolygonDataWithDividend,
    fundamentals: {
      ...mockPolygonDataWithDividend.fundamentals,
      dividendYield: 1.8 // Low dividend yield - should trigger neutral signal
    }
  };
  
  try {
    console.log("📊 Executing low dividend yield test...");
    console.log(`📈 Test data: AAPL with ${lowDividendData.fundamentals.dividendYield}% dividend yield`);
    
    const result = await executeAgent(testDividendYieldAgent, lowDividendData);
    
    console.log("\n✅ Agent execution completed successfully!");
    console.log("📈 Result Summary:");
    console.log(`   Symbol: ${lowDividendData.symbol}`);
    console.log(`   Dividend Yield: ${lowDividendData.fundamentals.dividendYield}%`);
    console.log(`   Signal: ${result.signal}`);
    console.log(`   Confidence: ${result.confidence}%`);
    
    // Verify the result
    const expectedSignal = lowDividendData.fundamentals.dividendYield > 3 ? 'bullish' : 'neutral';
    const isCorrect = result.signal === expectedSignal;
    
    console.log("\n🎯 Test Results:");
    console.log(`   Expected Signal: ${expectedSignal}`);
    console.log(`   Actual Signal: ${result.signal}`);
    console.log(`   Test Result: ${isCorrect ? '✅ PASSED' : '❌ FAILED'}`);
    
    return isCorrect;
    
  } catch (error) {
    console.error("\n❌ Low dividend yield test failed with error:");
    console.error(error);
    return false;
  }
}

// Run all dividend yield tests
async function runDividendYieldTests() {
  console.log("🚀 Starting Dividend Yield Tests");
  console.log("=" .repeat(60));

  const highDividendTest = await testDividendYield();
  const bugFixTest = await testIncorrectStatementConfig();
  const noDividendTest = await testNoDividendYield();
  const lowDividendTest = await testLowDividendYield();

  console.log("\n" + "=" .repeat(60));
  console.log("📊 DIVIDEND YIELD TEST RESULTS");
  console.log("=" .repeat(60));
  console.log(`High Dividend Yield Test: ${highDividendTest ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Bug Fix Test: ${bugFixTest ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`No Dividend Test: ${noDividendTest ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Low Dividend Test: ${lowDividendTest ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Overall Status: ${highDividendTest && bugFixTest && noDividendTest && lowDividendTest ? '🎉 ALL TESTS PASSED' : '⚠️ SOME TESTS FAILED'}`);

  return highDividendTest && bugFixTest && noDividendTest && lowDividendTest;
}

// Export for use in other test files
export { testDividendYield, testIncorrectStatementConfig, testNoDividendYield, testLowDividendYield, runDividendYieldTests };

// Run tests if this file is executed directly
if (import.meta.main) {
  runDividendYieldTests();
}
