import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': '*'
};

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
const supabase = createClient(supabaseUrl, supabaseServiceKey)

interface ChartDataPoint {
  name: string;
  value: number;
  date?: string;
  category?: string;
  color?: string;
}

interface ChartConfig {
  title: string;
  subtitle?: string;
  type: 'bar' | 'line' | 'area' | 'stacked-bar' | 'comparison-bar';
  data: ChartDataPoint[] | ChartDataPoint[][];
  colors?: string[];
  showValues?: boolean;
  showLegend?: boolean;
  showGrid?: boolean;
  backgroundColor?: string;
  textColor?: string;
  width?: number;
  height?: number;
  brandText?: string;
  totalChange?: string;
  cagr?: string;
  legendData?: string[];
  barWidth?: number;
  barGap?: string;
  categoryGap?: string;
}

/**
 * Create a comprehensive prompt for chart data generation
 */
function createChartDataPrompt(userQuery: string): string {
  return `You are an expert data analyst and chart generator with access to real-time data through Google Search. Based on the user's request, generate appropriate chart data and configuration using REAL, CURRENT, ACCURATE data.

User Request: "${userQuery}"

CRITICAL REQUIREMENTS:
1. Use Google Search to find REAL, CURRENT data - do NOT make up or estimate numbers
2. For stock prices, financial data, economic indicators - get the actual current values
3. For company comparisons - use real market data, revenue figures, stock prices
4. For historical trends - use actual historical data points
5. Cite your data sources when possible

Your task is to:
1. Search for and gather REAL data related to the user's request
2. Transform that real data into chart format
3. Choose the appropriate chart type and styling
4. Provide accurate titles and context

IMPORTANT: You must respond with a valid JSON object in this exact format:

For single data series (bar, line, area, stacked-bar):
{
  "reasoning": "Brief explanation of your interpretation and approach",
  "chartConfig": {
    "title": "Chart Title",
    "subtitle": "Chart Subtitle (optional)",
    "type": "bar|line|area|stacked-bar",
    "data": [
      {"name": "Period/Category 1", "value": 100},
      {"name": "Period/Category 2", "value": 150},
      {"name": "Period/Category 3", "value": 200}
    ],
    "colors": ["#3B82F6", "#10B981", "#F59E0B"],
    "showValues": true,
    "showGrid": true,
    "backgroundColor": "#FFFFFF",
    "textColor": "#000000",
    "width": 800,
    "height": 600,
    "brandText": "Made with Osis.co",
    "totalChange": "25.5%",
    "cagr": "12.3%"
  }
}

For comparison charts (comparison-bar - STACKED BY TIME PERIOD):
{
  "reasoning": "Brief explanation of your interpretation and approach",
  "chartConfig": {
    "title": "Apple vs Microsoft Revenue Comparison",
    "subtitle": "Quarterly Revenue Over Time (optional)",
    "type": "comparison-bar",
    "data": [
      [
        {"name": "Q1 '23", "value": 117154},
        {"name": "Q2 '23", "value": 81797},
        {"name": "Q3 '23", "value": 89498},
        {"name": "Q4 '23", "value": 119575}
      ],
      [
        {"name": "Q1 '23", "value": 52857},
        {"name": "Q2 '23", "value": 56189},
        {"name": "Q3 '23", "value": 61858},
        {"name": "Q4 '23", "value": 62020}
      ]
    ],
    "colors": ["#3B82F6", "#EF4444"],
    "showValues": true,
    "showLegend": true,
    "showGrid": true,
    "backgroundColor": "#FFFFFF",
    "textColor": "#000000",
    "width": 800,
    "height": 600,
    "brandText": "Made with Osis.co",
    "legendData": ["Apple Revenue (Millions)", "Microsoft Revenue (Millions)"]
  }
}

CRITICAL: For comparison charts, ALWAYS use time periods (quarters, years, months) on X-axis, NOT company names. Each time period should have multiple companies stacked. This creates charts like "Advertising Revenue (Google, Meta, Amazon, YouTube)" where each quarter shows all companies stacked together.

Guidelines for REAL DATA:
- Search for actual current stock prices, market caps, financial reports
- Use real company earnings, revenue, and financial metrics from recent quarters
- For economic data: Get actual GDP, inflation, unemployment rates from government sources
- For comparisons: Use real market data, not estimates
- Include actual dates and time periods
- For growth metrics: Calculate real totalChange and CAGR from actual data
- Choose colors that match the data theme (blue for revenue, green for profit, red for losses, etc.)
- If you cannot find real data, clearly state "Data not available" rather than making up numbers

Data Types You Can Generate:
- Company financials (revenue, profit, expenses)
- Market comparisons (company vs company, sector analysis) - USE COMPARISON-BAR
- Economic indicators (GDP, inflation, unemployment)
- Technology metrics (user growth, adoption rates)
- Industry trends (market size, growth rates)
- Performance metrics (any measurable data over time)

COMPARISON EXAMPLES (use comparison-bar for these):
- "Apple vs Microsoft quarterly revenue" → Quarters on X-axis, companies stacked
- "Tesla vs Ford quarterly sales comparison" → Quarters on X-axis, companies stacked
- "Netflix vs Disney subscriber growth over time" → Time periods on X-axis, companies stacked
- "Compare Amazon and Google quarterly revenue" → Quarters on X-axis, companies stacked
- "iPhone vs Samsung market share by quarter" → Quarters on X-axis, companies stacked
- "Bitcoin vs Ethereum price comparison over months" → Months on X-axis, cryptocurrencies stacked

Remember: Always use TIME PERIODS on X-axis, never company names!

Chart Types:
- "bar": Best for single metrics over time or simple comparisons
- "line": Best for trends and continuous data
- "area": Best for cumulative data and filled trends
- "stacked-bar": Best for showing composition and parts of a whole
- "comparison-bar": Best for comparing 2+ entities with STACKED bars (use when user asks for "vs", "compare", "comparison", "against", etc.)

WHEN TO USE COMPARISON-BAR:
- User asks to compare companies (e.g., "Apple vs Microsoft revenue")
- User asks to compare products, metrics, or performance
- User uses words like "vs", "versus", "compare", "comparison", "against"
- User wants to see multiple entities stacked together for comparison
- Data involves 2 or more distinct series that should be compared

For comparison-bar charts - CRITICAL DATA STRUCTURE:
- X-axis MUST be time periods: ["Q1 '23", "Q2 '23", "Q3 '23", "Q4 '23"]
- Each company gets its own data series with the SAME time periods
- Use array of arrays for data: [[appleQuarterlyData], [microsoftQuarterlyData], ...]
- Set showLegend: true
- Provide legendData with company names: ["Apple Revenue", "Microsoft Revenue"]
- Use contrasting colors for visual distinction
- Creates STACKED bars where companies stack within each time period
- Perfect for showing how companies perform over the same time periods

WRONG: X-axis = ["Apple", "Microsoft"] (this creates separate bars)
RIGHT: X-axis = ["Q1 '23", "Q2 '23", "Q3 '23"] with Apple and Microsoft stacked in each quarter

Remember: Generate realistic data that makes sense for the request. If the user asks for specific companies or time periods, try to reflect realistic market conditions and trends.

Respond with ONLY the JSON object, no additional text.`;
}

/**
 * Generate chart data using Gemini AI
 */
async function generateChartWithGemini(query: string, apiKey: string) {
  try {
    const prompt = createChartDataPrompt(query);

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        tools: [{ googleSearch: {} }],
        generationConfig: {
          temperature: 0.1, // Lower temperature for more accurate data
          maxOutputTokens: 2048,
          topK: 1,
          topP: 0.95
        }
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Gemini API error response:', errorText);
      throw new Error(`Gemini API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    const responseText = data.candidates?.[0]?.content?.parts?.[0]?.text || '';
    
    if (!responseText) {
      throw new Error('No response received from Gemini');
    }

    // Parse the JSON response
    const jsonMatch = responseText.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No JSON found in Gemini response');
    }

    const parsedData = JSON.parse(jsonMatch[0]);
    
    if (!parsedData.chartConfig) {
      throw new Error('No chart configuration found in response');
    }

    // Ensure brandText is set correctly
    parsedData.chartConfig.brandText = 'Made with Osis.co';

    return {
      success: true,
      chartConfig: parsedData.chartConfig,
      reasoning: parsedData.reasoning
    };

  } catch (error) {
    console.error('Error generating chart with Gemini:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    })
  }

  try {
    // Check for authentication
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'No authorization header' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // Verify user authentication
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);

    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // Parse request body
    const { query } = await req.json();

    if (!query || typeof query !== 'string') {
      return new Response(JSON.stringify({ error: 'Query is required' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // Get Gemini API key
    const geminiApiKey = Deno.env.get('GEMINI_API_KEY');
    if (!geminiApiKey) {
      return new Response(JSON.stringify({ error: 'Gemini API key not configured' }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // Generate chart with Gemini
    const result = await generateChartWithGemini(query, geminiApiKey);

    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })

  } catch (error) {
    console.error('Error in ai-chart-generator:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: error.message 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
})
