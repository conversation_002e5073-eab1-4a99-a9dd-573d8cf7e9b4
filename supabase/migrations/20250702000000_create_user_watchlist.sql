-- Create user_watchlist table for storing user's watchlisted stocks
CREATE TABLE IF NOT EXISTS user_watchlist (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    symbol VARCHAR(10) NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(255),
    current_price DECIMAL(10,2) DEFAULT 0,
    price_change DECIMAL(10,2) DEFAULT 0,
    price_change_percent DECIMAL(5,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique symbol per user
    UNIQUE(user_id, symbol)
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_user_watchlist_user_id ON user_watchlist(user_id);
CREATE INDEX IF NOT EXISTS idx_user_watchlist_symbol ON user_watchlist(symbol);
CREATE INDEX IF NOT EXISTS idx_user_watchlist_created_at ON user_watchlist(created_at);

-- Enable RLS (Row Level Security)
ALTER TABLE user_watchlist ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own watchlist" ON user_watchlist
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert into their own watchlist" ON user_watchlist
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own watchlist" ON user_watchlist
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete from their own watchlist" ON user_watchlist
    FOR DELETE USING (auth.uid() = user_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_user_watchlist_updated_at
    BEFORE UPDATE ON user_watchlist
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
