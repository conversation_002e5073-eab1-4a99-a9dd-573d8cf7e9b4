// Test to verify AND blocks can accept connections from various block types
console.log('Testing AND block connection compatibility...');

// Mock the compatibility matrix from BuildCanvas.tsx
const BLOCK_COMPATIBILITY = {
  'PRICE': ['CONDITION', 'COMPARISON', '<PERSON><PERSON><PERSON>_PATTERN', 'CHART_PATTERN', 'BREAKOUT_DETECTION', 'TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'AND', 'OR'],
  'MOVING_AVERAGE': ['CONDITION', 'COMPARISON', 'CA<PERSON>LE_PATTERN', 'CHART_PATTERN', 'BREAKOUT_DETECTION', 'TRIGGER', 'SIGNAL', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'AND', 'OR'],
  'MOMENTUM_INDICATOR': ['CONDITION', 'COMPARISON', '<PERSON><PERSON><PERSON>_PATTERN', 'CHART_PATTERN', 'BREAKOUT_DETECTION', 'TRIGGER', '<PERSON>IGNA<PERSON>', '<PERSON><PERSON>LL<PERSON>H_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'AND', 'OR'],
  'TREND_INDICATOR': ['CONDITION', 'COMPARISON', 'CANDLE_PATTERN', 'CHART_PATTERN', 'BREAKOUT_DETECTION', 'TRIGGER', 'SIGNAL', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'AND', 'OR'],
  'VOLUME_INDICATOR': ['CONDITION', 'COMPARISON', 'CANDLE_PATTERN', 'CHART_PATTERN', 'BREAKOUT_DETECTION', 'TRIGGER', 'SIGNAL', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'AND', 'OR'],
  'VOLATILITY_INDICATOR': ['CONDITION', 'COMPARISON', 'CANDLE_PATTERN', 'CHART_PATTERN', 'BREAKOUT_DETECTION', 'TRIGGER', 'SIGNAL', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'AND', 'OR'],
  'FUNDAMENTAL': ['CONDITION', 'COMPARISON', 'BREAKOUT_DETECTION', 'TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'AND', 'OR'],
  'INDICATOR': ['CONDITION', 'COMPARISON', 'CANDLE_PATTERN', 'CHART_PATTERN', 'BREAKOUT_DETECTION', 'TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'AND', 'OR'],
  'COMPARISON': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
  'CONDITION': ['AND', 'OR', 'TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST'],
  'BREAKOUT_DETECTION': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
  'CANDLE_PATTERN': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
  'CHART_PATTERN': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
  'STOCK_SENTIMENT': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
  'GAP_ANALYSIS': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
  'SUPPORT_RESISTANCE': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
  'TREND_LINE_ANALYSIS': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
  'MARKET_STRUCTURE': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
  'PRICE_ACTION_SIGNAL': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
  'MULTI_TIMEFRAME_ANALYSIS': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
  'DIVERGENCE_DETECTION': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
  'VOLUME_CONFIRMATION': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
  'MARKET_REGIME': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
  'NOT_OPERATOR': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
  'TIME_FILTER': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
  'MARKET_CONDITION_FILTER': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
  'BULLISH_CONFIDENCE_BOOST': ['TRIGGER', 'BREAKOUT_DETECTION', 'CANDLE_PATTERN', 'CHART_PATTERN', 'CONDITION', 'COMPARISON', 'AND', 'OR'],
  'BEARISH_CONFIDENCE_BOOST': ['TRIGGER', 'BREAKOUT_DETECTION', 'CANDLE_PATTERN', 'CHART_PATTERN', 'CONDITION', 'COMPARISON', 'AND', 'OR'],
  'CONFIDENCE_BOOST': ['TRIGGER', 'BREAKOUT_DETECTION', 'CANDLE_PATTERN', 'CHART_PATTERN', 'CONDITION', 'COMPARISON', 'AND', 'OR'],
  'AND': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR'],
  'OR': ['TRIGGER', 'BULLISH_CONFIDENCE_BOOST', 'BEARISH_CONFIDENCE_BOOST', 'CONFIDENCE_BOOST', 'AND', 'OR']
};

// Test cases: blocks that should be able to connect to AND blocks
const testCases = [
  // Data blocks
  { sourceType: 'PRICE', targetType: 'AND', shouldConnect: true },
  { sourceType: 'FUNDAMENTAL', targetType: 'AND', shouldConnect: true },
  { sourceType: 'INDICATOR', targetType: 'AND', shouldConnect: true },
  
  // Technical indicator blocks
  { sourceType: 'MOMENTUM_INDICATOR', targetType: 'AND', shouldConnect: true },
  { sourceType: 'MOVING_AVERAGE', targetType: 'AND', shouldConnect: true },
  { sourceType: 'TREND_INDICATOR', targetType: 'AND', shouldConnect: true },
  { sourceType: 'VOLUME_INDICATOR', targetType: 'AND', shouldConnect: true },
  { sourceType: 'VOLATILITY_INDICATOR', targetType: 'AND', shouldConnect: true },
  
  // Analysis blocks
  { sourceType: 'CONDITION', targetType: 'AND', shouldConnect: true },
  { sourceType: 'COMPARISON', targetType: 'AND', shouldConnect: true },
  { sourceType: 'BREAKOUT_DETECTION', targetType: 'AND', shouldConnect: true },
  
  // Pattern blocks
  { sourceType: 'CANDLE_PATTERN', targetType: 'AND', shouldConnect: true },
  { sourceType: 'CHART_PATTERN', targetType: 'AND', shouldConnect: true },
  { sourceType: 'STOCK_SENTIMENT', targetType: 'AND', shouldConnect: true },
  { sourceType: 'GAP_ANALYSIS', targetType: 'AND', shouldConnect: true },
  
  // Market analysis blocks
  { sourceType: 'SUPPORT_RESISTANCE', targetType: 'AND', shouldConnect: true },
  { sourceType: 'TREND_LINE_ANALYSIS', targetType: 'AND', shouldConnect: true },
  { sourceType: 'MARKET_STRUCTURE', targetType: 'AND', shouldConnect: true },
  
  // Signal generation blocks
  { sourceType: 'PRICE_ACTION_SIGNAL', targetType: 'AND', shouldConnect: true },
  { sourceType: 'MULTI_TIMEFRAME_ANALYSIS', targetType: 'AND', shouldConnect: true },
  { sourceType: 'DIVERGENCE_DETECTION', targetType: 'AND', shouldConnect: true },
  { sourceType: 'VOLUME_CONFIRMATION', targetType: 'AND', shouldConnect: true },
  { sourceType: 'MARKET_REGIME', targetType: 'AND', shouldConnect: true },
  
  // Logic blocks
  { sourceType: 'NOT_OPERATOR', targetType: 'AND', shouldConnect: true },
  { sourceType: 'TIME_FILTER', targetType: 'AND', shouldConnect: true },
  { sourceType: 'MARKET_CONDITION_FILTER', targetType: 'AND', shouldConnect: true },
  
  // Confidence boost blocks
  { sourceType: 'BULLISH_CONFIDENCE_BOOST', targetType: 'AND', shouldConnect: true },
  { sourceType: 'BEARISH_CONFIDENCE_BOOST', targetType: 'AND', shouldConnect: true },
  { sourceType: 'CONFIDENCE_BOOST', targetType: 'AND', shouldConnect: true },
  
  // Logic blocks to logic blocks
  { sourceType: 'AND', targetType: 'AND', shouldConnect: true },
  { sourceType: 'OR', targetType: 'AND', shouldConnect: true },
  
  // Terminal blocks (should NOT connect to anything)
  { sourceType: 'TRIGGER', targetType: 'AND', shouldConnect: false },
];

// Function to test connection validity
function canConnect(sourceType, targetType) {
  const allowedTargets = BLOCK_COMPATIBILITY[sourceType];
  if (!allowedTargets) {
    return false;
  }
  return allowedTargets.includes(targetType);
}

// Run tests
let passedTests = 0;
let failedTests = 0;

console.log('\n🧪 Running connection compatibility tests...\n');

testCases.forEach(({ sourceType, targetType, shouldConnect }, index) => {
  const actualCanConnect = canConnect(sourceType, targetType);
  const testPassed = actualCanConnect === shouldConnect;
  
  if (testPassed) {
    console.log(`✅ Test ${index + 1}: ${sourceType} → ${targetType} (${shouldConnect ? 'allowed' : 'blocked'})`);
    passedTests++;
  } else {
    console.log(`❌ Test ${index + 1}: ${sourceType} → ${targetType} - Expected: ${shouldConnect}, Got: ${actualCanConnect}`);
    failedTests++;
  }
});

// Summary
console.log(`\n📊 Test Results:`);
console.log(`✅ Passed: ${passedTests}`);
console.log(`❌ Failed: ${failedTests}`);
console.log(`📈 Success Rate: ${((passedTests / (passedTests + failedTests)) * 100).toFixed(1)}%`);

if (failedTests === 0) {
  console.log('\n🎉 All tests passed! AND blocks can now accept connections from various block types.');
} else {
  console.log('\n⚠️  Some tests failed. Check the compatibility matrix.');
}

// Test AND block execution logic with different input types
console.log('\n🔧 Testing AND block execution logic with different input types...\n');

// Mock executeAndBlock function (simplified version)
function executeAndBlock(inputValues) {
  // Convert all input values to boolean and check if ALL are true
  const booleanInputs = inputValues.map(val => !!val);
  const result = booleanInputs.every(val => val === true);
  
  console.log(`AND operation: inputs [${inputValues.join(', ')}] → booleans [${booleanInputs.join(', ')}] → result: ${result}`);
  return result;
}

// Test different input types
const executionTests = [
  { inputs: [true, true], expected: true },
  { inputs: [true, false], expected: false },
  { inputs: [1, 1], expected: true },
  { inputs: [1, 0], expected: false },
  { inputs: ['yes', 'no'], expected: false },
  { inputs: ['yes', 'maybe'], expected: true },
  { inputs: [null, undefined], expected: false },
  { inputs: [42, 'hello', true], expected: true },
  { inputs: [0, '', false], expected: false },
];

let executionPassed = 0;
let executionFailed = 0;

executionTests.forEach(({ inputs, expected }, index) => {
  const result = executeAndBlock(inputs);
  const testPassed = result === expected;
  
  if (testPassed) {
    console.log(`✅ Execution Test ${index + 1}: Passed`);
    executionPassed++;
  } else {
    console.log(`❌ Execution Test ${index + 1}: Expected ${expected}, Got ${result}`);
    executionFailed++;
  }
});

console.log(`\n📊 Execution Test Results:`);
console.log(`✅ Passed: ${executionPassed}`);
console.log(`❌ Failed: ${executionFailed}`);

if (executionFailed === 0) {
  console.log('\n🎉 All execution tests passed! AND block can handle different input types correctly.');
} else {
  console.log('\n⚠️  Some execution tests failed.');
}

console.log('\n✨ Test completed!');
