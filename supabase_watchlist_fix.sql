-- CORRECTED Supabase SQL Setup for Trade Sensei Watchlist
-- Run this in your Supabase SQL Editor to fix watchlist issues

-- 1. Drop existing functions first to avoid conflicts
DROP FUNCTION IF EXISTS get_user_watchlist(UUID);
DROP FUNCTION IF EXISTS add_to_watchlist(U<PERSON><PERSON>, VA<PERSON>HA<PERSON>, VA<PERSON><PERSON><PERSON>);
DROP FUNCTION IF EXISTS remove_from_watchlist(U<PERSON>D, VARCHAR);
DROP FUNCTION IF EXISTS is_in_watchlist(UUID, VARCHAR);

-- 2. Create the user_watchlist table (matching existing service exactly)
CREATE TABLE IF NOT EXISTS public.user_watchlist (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    symbol VARCHAR(10) NOT NULL,
    name VARCHAR(255) NOT NULL,
    current_price DECIMAL(10,4) DEFAULT 0,
    price_change DECIMAL(10,4) DEFAULT 0,
    price_change_percent DECIMAL(5,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique symbol per user
    UNIQUE(user_id, symbol)
);

-- 3. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_watchlist_user_id ON public.user_watchlist(user_id);
CREATE INDEX IF NOT EXISTS idx_user_watchlist_symbol ON public.user_watchlist(symbol);
CREATE INDEX IF NOT EXISTS idx_user_watchlist_created_at ON public.user_watchlist(created_at DESC);

-- 4. Enable Row Level Security (RLS)
ALTER TABLE public.user_watchlist ENABLE ROW LEVEL SECURITY;

-- 5. Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view own watchlist" ON public.user_watchlist;
DROP POLICY IF EXISTS "Users can insert own watchlist" ON public.user_watchlist;
DROP POLICY IF EXISTS "Users can update own watchlist" ON public.user_watchlist;
DROP POLICY IF EXISTS "Users can delete own watchlist" ON public.user_watchlist;

-- 6. Create RLS policies
CREATE POLICY "Users can view own watchlist" ON public.user_watchlist
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own watchlist" ON public.user_watchlist
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own watchlist" ON public.user_watchlist
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own watchlist" ON public.user_watchlist
    FOR DELETE USING (auth.uid() = user_id);

-- 7. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON public.user_watchlist TO authenticated;

-- 8. Create a simple function to check if everything is working
CREATE OR REPLACE FUNCTION test_watchlist_setup()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Test if table exists and is accessible
    PERFORM 1 FROM public.user_watchlist LIMIT 1;
    RETURN 'Watchlist setup is working correctly! ✅';
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'Watchlist setup failed: ' || SQLERRM;
END;
$$;

-- 9. Test the setup
SELECT test_watchlist_setup();

-- 10. Insert a test record to verify everything works (replace with your actual user ID)
-- Uncomment and replace the UUID with your actual user ID from auth.users
/*
INSERT INTO public.user_watchlist (user_id, symbol, name) 
VALUES ('YOUR_USER_ID_HERE', 'AAPL', 'Apple Inc.')
ON CONFLICT (user_id, symbol) DO NOTHING;
*/

-- 11. Show current table structure
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'user_watchlist' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Success message
SELECT 'Watchlist database setup completed successfully! 🎉' as status;
